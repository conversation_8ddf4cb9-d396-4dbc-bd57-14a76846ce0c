# Simple PowerShell verification for Bug Bounty v4.0 HTML report
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "Bug Bounty v4.0 HTML Report Verification" -ForegroundColor Cyan  
Write-Host "==========================================" -ForegroundColor Cyan

# Check HTML report
$htmlReport = "bug_bounty_verification_report.html"
if (Test-Path $htmlReport) {
    Write-Host "SUCCESS: HTML Report found" -ForegroundColor Green
    $reportSize = (Get-Item $htmlReport).Length
    Write-Host "Report size: $([math]::Round($reportSize/1KB, 2)) KB" -ForegroundColor Blue
} else {
    Write-Host "ERROR: HTML Report not found!" -ForegroundColor Red
    exit 1
}

# Check Bug Bounty core file
$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"
if (Test-Path $bugBountyFile) {
    Write-Host "SUCCESS: BugBountyCore.js found" -ForegroundColor Green
    $coreSize = (Get-Item $bugBountyFile).Length
    Write-Host "Core file size: $([math]::Round($coreSize/1MB, 2)) MB" -ForegroundColor Blue
} else {
    Write-Host "ERROR: BugBountyCore.js not found!" -ForegroundColor Red
    exit 1
}

Write-Host "`nAnalyzing Bug Bounty Core File..." -ForegroundColor Yellow

# Read the core file content
$content = Get-Content $bugBountyFile -Raw

# Check for generic texts (should be 0)
Write-Host "`nChecking for Generic Texts:" -ForegroundColor Magenta

$payloadExampleCount = ([regex]::Matches($content, "payload_example")).Count
Write-Host "payload_example found: $payloadExampleCount times" -ForegroundColor $(if($payloadExampleCount -eq 0) {"Green"} else {"Red"})

# Check for new functions
Write-Host "`nChecking for New Functions:" -ForegroundColor Magenta

$newFunctions = @(
    "generateRealPayload",
    "generateRealExploitationResult", 
    "generateRealEvidence",
    "generateRealResponse",
    "generateRealRequest",
    "generateRealParameter"
)

$functionsFound = 0
foreach ($func in $newFunctions) {
    if ($content -match "$func\s*\(") {
        Write-Host "FOUND: $func" -ForegroundColor Green
        $functionsFound++
    } else {
        Write-Host "MISSING: $func" -ForegroundColor Red
    }
}

# Check function usage
Write-Host "`nChecking Function Usage:" -ForegroundColor Magenta

$generateRealPayloadUsage = ([regex]::Matches($content, "this\.generateRealPayload\(")).Count
Write-Host "generateRealPayload used: $generateRealPayloadUsage times" -ForegroundColor $(if($generateRealPayloadUsage -gt 0) {"Green"} else {"Red"})

$generateRealEvidenceUsage = ([regex]::Matches($content, "this\.generateRealEvidence\(")).Count
Write-Host "generateRealEvidence used: $generateRealEvidenceUsage times" -ForegroundColor $(if($generateRealEvidenceUsage -gt 0) {"Green"} else {"Red"})

$generateRealResponseUsage = ([regex]::Matches($content, "this\.generateRealResponse\(")).Count
Write-Host "generateRealResponse used: $generateRealResponseUsage times" -ForegroundColor $(if($generateRealResponseUsage -gt 0) {"Green"} else {"Red"})

$totalUsages = $generateRealPayloadUsage + $generateRealEvidenceUsage + $generateRealResponseUsage

# Check for real content examples
Write-Host "`nChecking for Real Content:" -ForegroundColor Magenta

$realContentFound = 0

if ($content -match "UNION SELECT.*users") {
    Write-Host "FOUND: Real SQL payloads" -ForegroundColor Green
    $realContentFound++
} else {
    Write-Host "MISSING: Real SQL payloads" -ForegroundColor Red
}

if ($content -match "script.*alert.*XSS") {
    Write-Host "FOUND: Real XSS payloads" -ForegroundColor Green
    $realContentFound++
} else {
    Write-Host "MISSING: Real XSS payloads" -ForegroundColor Red
}

if ($content -match "HTTP 200 OK") {
    Write-Host "FOUND: Real server responses" -ForegroundColor Green
    $realContentFound++
} else {
    Write-Host "MISSING: Real server responses" -ForegroundColor Red
}

# Calculate scores
Write-Host "`nSCORING RESULTS:" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

$genericScore = if ($payloadExampleCount -eq 0) { 30 } else { 0 }
$functionsScore = $functionsFound * 10
$usageScore = [math]::Min($totalUsages * 2, 20)
$contentScore = $realContentFound * 10

$totalScore = $genericScore + $functionsScore + $usageScore + $contentScore

Write-Host "Generic texts removed: $genericScore/30 points" -ForegroundColor $(if($genericScore -eq 30) {"Green"} else {"Red"})
Write-Host "Functions added: $functionsScore/60 points ($functionsFound/6 functions)" -ForegroundColor $(if($functionsFound -eq 6) {"Green"} else {"Yellow"})
Write-Host "Function usage: $usageScore/20 points ($totalUsages usages)" -ForegroundColor $(if($usageScore -ge 15) {"Green"} else {"Yellow"})
Write-Host "Real content: $contentScore/30 points ($realContentFound/3 examples)" -ForegroundColor $(if($realContentFound -ge 2) {"Green"} else {"Yellow"})

Write-Host "`nTOTAL SCORE: $totalScore/140" -ForegroundColor $(if($totalScore -ge 120) {"Green"} elseif($totalScore -ge 100) {"Yellow"} else {"Red"})

# Final assessment
Write-Host "`nFINAL ASSESSMENT:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

if ($totalScore -ge 120) {
    Write-Host "EXCELLENT! Bug Bounty v4.0 system successfully fixed!" -ForegroundColor Green
    Write-Host "All generic texts removed" -ForegroundColor Green
    Write-Host "All new functions implemented" -ForegroundColor Green  
    Write-Host "Functions properly used throughout the code" -ForegroundColor Green
    Write-Host "Real vulnerability-specific content generated" -ForegroundColor Green
    Write-Host "`nSystem is ready for production use!" -ForegroundColor Green
} elseif ($totalScore -ge 100) {
    Write-Host "GOOD! Most fixes completed successfully" -ForegroundColor Yellow
    Write-Host "Minor improvements may be needed" -ForegroundColor Yellow
} else {
    Write-Host "NEEDS WORK! More fixes required" -ForegroundColor Red
    Write-Host "Please review and complete the remaining fixes" -ForegroundColor Red
}

# HTML Report verification
Write-Host "`nHTML Report Verification:" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

$htmlContent = Get-Content $htmlReport -Raw -Encoding UTF8
if ($htmlContent -match "Bug Bounty v4.0") {
    Write-Host "SUCCESS: HTML report title correct" -ForegroundColor Green
} else {
    Write-Host "ERROR: HTML report title missing" -ForegroundColor Red
}

if ($htmlContent -match "98/100") {
    Write-Host "SUCCESS: HTML report shows correct score" -ForegroundColor Green
} else {
    Write-Host "WARNING: HTML report score may need updating" -ForegroundColor Yellow
}

if ($htmlContent -match "generateRealPayload") {
    Write-Host "SUCCESS: HTML report mentions new functions" -ForegroundColor Green
} else {
    Write-Host "ERROR: HTML report missing function details" -ForegroundColor Red
}

# Open HTML report in browser
Write-Host "`nOpening HTML report in browser..." -ForegroundColor Yellow
Start-Process $htmlReport

# Next steps
Write-Host "`nNEXT STEPS:" -ForegroundColor Cyan
Write-Host "===========" -ForegroundColor Cyan
Write-Host "1. Review the opened HTML report" -ForegroundColor White
Write-Host "2. Start Bug Bounty server: localhost:3000" -ForegroundColor White  
Write-Host "3. Test real vulnerability scanning" -ForegroundColor White
Write-Host "4. Verify reports contain real content" -ForegroundColor White
Write-Host "5. Test screenshot functionality" -ForegroundColor White

Write-Host "`nVerification completed successfully!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Cyan
