<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح خطأ vuln is not defined</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.2);
            border-left-color: #f44336;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border-left-color: #4CAF50;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left-color: #ff9800;
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        h1, h2 {
            text-align: center;
            margin-bottom: 30px;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح خطأ vuln is not defined</h1>
        
        <div class="test-section">
            <h2>📊 معلومات النظام</h2>
            <div id="systemInfo"></div>
        </div>

        <div class="test-section">
            <h2>🔍 اختبارات الأخطاء</h2>
            <button onclick="testVulnError()">اختبار خطأ vuln is not defined</button>
            <button onclick="testGenerateImpact()">اختبار generateImpactFromTechnique</button>
            <button onclick="testExtractVulnerabilities()">اختبار extractAllVulnerabilitiesFromExpandedPrompt</button>
            <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>📋 سجل الأخطاء</h2>
            <div class="log" id="errorLog"></div>
        </div>

        <div class="test-section">
            <h2>✅ نتائج الإصلاح</h2>
            <div id="fixResults"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let testResults = [];
        let currentTest = 0;
        let totalTests = 4;

        // تهيئة النظام
        async function initializeSystem() {
            try {
                updateSystemInfo('🔄 تهيئة BugBountyCore...');
                bugBountyCore = new BugBountyCore();
                updateSystemInfo('✅ تم تهيئة BugBountyCore بنجاح');
                return true;
            } catch (error) {
                updateSystemInfo('❌ فشل في تهيئة BugBountyCore: ' + error.message);
                logError('خطأ في التهيئة: ' + error.message + '\nStack: ' + error.stack);
                return false;
            }
        }

        // تحديث معلومات النظام
        function updateSystemInfo(message) {
            document.getElementById('systemInfo').innerHTML = `
                <div class="status success">${message}</div>
                <p>الوقت: ${new Date().toLocaleString('ar')}</p>
                <p>المتصفح: ${navigator.userAgent}</p>
            `;
        }

        // تسجيل الأخطاء
        function logError(message) {
            const timestamp = new Date().toLocaleString('ar');
            const logElement = document.getElementById('errorLog');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // تحديث شريط التقدم
        function updateProgress() {
            const progress = (currentTest / totalTests) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        // اختبار خطأ vuln is not defined
        async function testVulnError() {
            currentTest++;
            updateProgress();
            
            const resultDiv = document.getElementById('testResults');
            resultDiv.innerHTML += '<div class="test-section warning">🔄 اختبار خطأ vuln is not defined...</div>';
            
            try {
                // محاولة استدعاء الدالة التي تسبب الخطأ
                const testPrompt = "SQL Injection, XSS, CSRF";
                const result = await bugBountyCore.extractAllVulnerabilitiesFromExpandedPrompt(testPrompt, 'http://testphp.vulnweb.com');
                
                resultDiv.innerHTML += '<div class="test-section success">✅ لا يوجد خطأ vuln is not defined</div>';
                testResults.push({test: 'vuln error', status: 'success', result: result});
                
            } catch (error) {
                if (error.message.includes('vuln is not defined')) {
                    resultDiv.innerHTML += '<div class="test-section error">❌ تم العثور على خطأ vuln is not defined</div>';
                    logError('خطأ vuln is not defined: ' + error.message + '\nStack: ' + error.stack);
                    testResults.push({test: 'vuln error', status: 'error', error: error.message});
                } else {
                    resultDiv.innerHTML += '<div class="test-section warning">⚠️ خطأ آخر: ' + error.message + '</div>';
                    logError('خطأ آخر: ' + error.message + '\nStack: ' + error.stack);
                    testResults.push({test: 'vuln error', status: 'other_error', error: error.message});
                }
            }
        }

        // اختبار generateImpactFromTechnique
        async function testGenerateImpact() {
            currentTest++;
            updateProgress();
            
            const resultDiv = document.getElementById('testResults');
            resultDiv.innerHTML += '<div class="test-section warning">🔄 اختبار generateImpactFromTechnique...</div>';
            
            try {
                const impact1 = bugBountyCore.generateImpactFromTechnique('SQL Injection');
                const impact2 = bugBountyCore.generateImpactFromTechnique('XSS');
                const impact3 = bugBountyCore.generateImpactFromTechnique('CSRF');
                
                resultDiv.innerHTML += `
                    <div class="test-section success">
                        ✅ generateImpactFromTechnique يعمل بشكل صحيح<br>
                        SQL: ${impact1}<br>
                        XSS: ${impact2}<br>
                        CSRF: ${impact3}
                    </div>
                `;
                testResults.push({test: 'generateImpact', status: 'success', results: {impact1, impact2, impact3}});
                
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-section error">❌ خطأ في generateImpactFromTechnique: ' + error.message + '</div>';
                logError('خطأ generateImpactFromTechnique: ' + error.message + '\nStack: ' + error.stack);
                testResults.push({test: 'generateImpact', status: 'error', error: error.message});
            }
        }

        // اختبار extractAllVulnerabilitiesFromExpandedPrompt
        async function testExtractVulnerabilities() {
            currentTest++;
            updateProgress();
            
            const resultDiv = document.getElementById('testResults');
            resultDiv.innerHTML += '<div class="test-section warning">🔄 اختبار extractAllVulnerabilitiesFromExpandedPrompt...</div>';
            
            try {
                const testPrompt = `
                    SQL Injection Testing:
                    - Union-based SQL Injection
                    - Boolean-based SQL Injection
                    - Time-based SQL Injection
                    
                    XSS Testing:
                    - Reflected XSS
                    - Stored XSS
                    - DOM-based XSS
                `;
                
                const vulnerabilities = await bugBountyCore.extractAllVulnerabilitiesFromExpandedPrompt(testPrompt, 'http://testphp.vulnweb.com');
                
                resultDiv.innerHTML += `
                    <div class="test-section success">
                        ✅ extractAllVulnerabilitiesFromExpandedPrompt يعمل بشكل صحيح<br>
                        عدد الثغرات المستخرجة: ${vulnerabilities.length}
                    </div>
                `;
                testResults.push({test: 'extractVulnerabilities', status: 'success', count: vulnerabilities.length});
                
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-section error">❌ خطأ في extractAllVulnerabilitiesFromExpandedPrompt: ' + error.message + '</div>';
                logError('خطأ extractAllVulnerabilitiesFromExpandedPrompt: ' + error.message + '\nStack: ' + error.stack);
                testResults.push({test: 'extractVulnerabilities', status: 'error', error: error.message});
            }
        }

        // تشغيل جميع الاختبارات
        async function runAllTests() {
            currentTest = 0;
            testResults = [];
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('fixResults').innerHTML = '';
            
            const initialized = await initializeSystem();
            if (!initialized) return;
            
            await testVulnError();
            await testGenerateImpact();
            await testExtractVulnerabilities();
            
            // عرض النتائج النهائية
            displayFinalResults();
        }

        // عرض النتائج النهائية
        function displayFinalResults() {
            const fixResultsDiv = document.getElementById('fixResults');
            const successCount = testResults.filter(r => r.status === 'success').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            
            let resultsHtml = `
                <h3>📊 ملخص النتائج</h3>
                <p>✅ اختبارات ناجحة: ${successCount}</p>
                <p>❌ اختبارات فاشلة: ${errorCount}</p>
                <p>📊 إجمالي الاختبارات: ${testResults.length}</p>
            `;
            
            if (errorCount === 0) {
                resultsHtml += '<div class="test-section success">🎉 جميع الاختبارات نجحت! لا توجد أخطاء vuln is not defined</div>';
            } else {
                resultsHtml += '<div class="test-section error">⚠️ توجد أخطاء تحتاج إلى إصلاح</div>';
            }
            
            resultsHtml += '<h4>تفاصيل النتائج:</h4>';
            testResults.forEach((result, index) => {
                const statusClass = result.status === 'success' ? 'success' : 'error';
                resultsHtml += `
                    <div class="test-section ${statusClass}">
                        <strong>اختبار ${index + 1}: ${result.test}</strong><br>
                        الحالة: ${result.status}<br>
                        ${result.error ? 'الخطأ: ' + result.error : ''}
                        ${result.count ? 'العدد: ' + result.count : ''}
                    </div>
                `;
            });
            
            fixResultsDiv.innerHTML = resultsHtml;
        }

        // تهيئة الصفحة
        window.onload = function() {
            updateSystemInfo('🔄 جاري تحميل الصفحة...');
            setTimeout(() => {
                updateSystemInfo('✅ تم تحميل الصفحة بنجاح - جاهز للاختبار');
            }, 1000);
        };
    </script>
</body>
</html>
