<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح خطأ "vuln is not defined"</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .test-button:hover {
            background: #218838;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح خطأ "vuln is not defined"</h1>
        <p>هذا الاختبار للتأكد من أن الخطأ قد تم إصلاحه بنجاح</p>

        <button class="test-button" onclick="testVulnerabilityExtraction()">🧪 اختبار استخراج الثغرات</button>
        <button class="test-button" onclick="testInteractiveDialogues()">💬 اختبار الحوارات التفاعلية</button>
        <button class="test-button" onclick="testFullWorkflow()">🚀 اختبار النظام الكامل</button>

        <div id="test-result" class="result info">جاهز للاختبار...</div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            try {
                bugBountyCore = new BugBountyCore();
                console.log('✅ تم تهيئة BugBountyCore بنجاح');
                updateResult('✅ تم تهيئة BugBountyCore بنجاح', 'success');
            } catch (error) {
                console.error('❌ فشل في تهيئة BugBountyCore:', error);
                updateResult(`❌ فشل في تهيئة BugBountyCore: ${error.message}`, 'error');
            }
        });

        function updateResult(message, type = 'info') {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        // اختبار استخراج الثغرات
        async function testVulnerabilityExtraction() {
            updateResult('🔄 جاري اختبار استخراج الثغرات...', 'info');
            
            try {
                // اختبار تحميل البرومبت
                const prompt = await bugBountyCore.loadPromptTemplate();
                console.log(`✅ تم تحميل البرومبت: ${prompt.length} حرف`);
                
                // اختبار استخراج الثغرات
                const vulnerabilities = await bugBountyCore.extractAllVulnerabilitiesFromPrompt(prompt, 'http://testphp.vulnweb.com');
                
                updateResult(`✅ نجح اختبار استخراج الثغرات!
📊 عدد الثغرات المكتشفة: ${vulnerabilities.length}
📝 طول البرومبت: ${prompt.length} حرف
🎯 لا توجد أخطاء JavaScript!`, 'success');
                
            } catch (error) {
                console.error('❌ خطأ في استخراج الثغرات:', error);
                updateResult(`❌ فشل اختبار استخراج الثغرات: ${error.message}
Stack: ${error.stack}`, 'error');
            }
        }

        // اختبار الحوارات التفاعلية
        async function testInteractiveDialogues() {
            updateResult('🔄 جاري اختبار الحوارات التفاعلية...', 'info');
            
            try {
                const testVulns = [{
                    name: 'SQL Injection',
                    type: 'SQL Injection',
                    payload: "' OR '1'='1' --",
                    severity: 'High'
                }];
                
                const dialogues = bugBountyCore.generateInteractiveDialogues(testVulns);
                
                updateResult(`✅ نجح اختبار الحوارات التفاعلية!
📊 طول النتيجة: ${dialogues.length} حرف
🎯 لا توجد أخطاء "vuln is not defined"!`, 'success');
                
            } catch (error) {
                console.error('❌ خطأ في الحوارات التفاعلية:', error);
                updateResult(`❌ فشل اختبار الحوارات التفاعلية: ${error.message}
Stack: ${error.stack}`, 'error');
            }
        }

        // اختبار النظام الكامل
        async function testFullWorkflow() {
            updateResult('🔄 جاري اختبار النظام الكامل...', 'info');
            
            try {
                // تحميل البرومبت
                const prompt = await bugBountyCore.loadPromptTemplate();
                
                // استخراج الثغرات
                const vulnerabilities = await bugBountyCore.extractAllVulnerabilitiesFromPrompt(prompt, 'http://testphp.vulnweb.com');
                
                // اختبار الدوال المختلفة
                const dialogues = bugBountyCore.generateInteractiveDialogues(vulnerabilities.slice(0, 3));
                const visualChanges = bugBountyCore.generateVisualChanges(vulnerabilities.slice(0, 3));
                const testingDetails = bugBountyCore.generateTestingDetails(vulnerabilities.slice(0, 3));
                
                updateResult(`✅ نجح اختبار النظام الكامل!
📊 عدد الثغرات: ${vulnerabilities.length}
💬 طول الحوارات: ${dialogues.length} حرف
🎨 طول التغيرات البصرية: ${visualChanges.length} حرف
🧪 طول تفاصيل الاختبار: ${testingDetails.length} حرف
🎉 تم إصلاح خطأ "vuln is not defined" بنجاح!`, 'success');
                
            } catch (error) {
                console.error('❌ خطأ في النظام الكامل:', error);
                updateResult(`❌ فشل اختبار النظام الكامل: ${error.message}
Stack: ${error.stack}`, 'error');
            }
        }
    </script>
</body>
</html>
