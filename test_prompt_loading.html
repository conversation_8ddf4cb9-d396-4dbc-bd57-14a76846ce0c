<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل البرومبت الديناميكي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            background: #fdf2f2;
        }
        .success { border-color: #27ae60; background: #f2fdf2; }
        .warning { border-color: #f39c12; background: #fdf9f2; }
        .error { border-color: #e74c3c; background: #fdf2f2; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover { background: #2980b9; }
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تحميل البرومبت الديناميكي - النظام v4</h1>
        
        <div class="test-section">
            <h2>📄 اختبار تحميل البرومبت</h2>
            <button onclick="testPromptLoading()">🔍 اختبار تحميل البرومبت</button>
            <button onclick="testVulnerabilityExtraction()">⚡ اختبار استخراج الثغرات</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
            <div id="promptLog" class="log"></div>
        </div>

        <div class="test-section">
            <h2>📊 نتائج الاختبار</h2>
            <div id="testResults"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('promptLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorMap = {
                'info': '#3498db',
                'success': '#27ae60',
                'warning': '#f39c12',
                'error': '#e74c3c'
            };
            
            logElement.innerHTML += `<span style="color: ${colorMap[type] || '#ecf0f1'}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('promptLog').innerHTML = '';
            document.getElementById('testResults').innerHTML = '';
        }

        async function initializeBugBounty() {
            if (!bugBountyCore) {
                log('🔧 تهيئة نظام Bug Bounty v4...', 'info');
                try {
                    bugBountyCore = new BugBountyCore();
                    log('✅ تم تهيئة النظام بنجاح', 'success');
                    return true;
                } catch (error) {
                    log(`❌ فشل في تهيئة النظام: ${error.message}`, 'error');
                    return false;
                }
            }
            return true;
        }

        async function testPromptLoading() {
            log('🧪 بدء اختبار تحميل البرومبت الديناميكي...', 'info');
            
            if (!await initializeBugBounty()) {
                return;
            }

            try {
                log('📄 محاولة تحميل البرومبت من prompt_template.txt...', 'info');
                const prompt = await bugBountyCore.loadPromptTemplate();
                
                if (prompt && prompt.length > 0) {
                    log(`✅ تم تحميل البرومبت بنجاح!`, 'success');
                    log(`📏 حجم البرومبت: ${prompt.length} حرف`, 'info');
                    log(`📊 عدد الأسطر: ${prompt.split('\n').length}`, 'info');
                    
                    // عرض بداية البرومبت
                    const preview = prompt.substring(0, 200) + '...';
                    log(`🔍 معاينة البرومبت:\n${preview}`, 'info');
                    
                    // تحديث النتائج
                    document.getElementById('testResults').innerHTML = `
                        <div class="success">
                            <h3>✅ نجح تحميل البرومبت الديناميكي</h3>
                            <p><strong>حجم البرومبت:</strong> ${prompt.length} حرف</p>
                            <p><strong>عدد الأسطر:</strong> ${prompt.split('\n').length}</p>
                            <p><strong>النظام:</strong> v4 الديناميكي جاهز للعمل</p>
                        </div>
                    `;
                } else {
                    log('❌ البرومبت فارغ أو غير صالح', 'error');
                    document.getElementById('testResults').innerHTML = `
                        <div class="error">
                            <h3>❌ فشل في تحميل البرومبت</h3>
                            <p>البرومبت فارغ أو غير صالح</p>
                        </div>
                    `;
                }
            } catch (error) {
                log(`❌ خطأ في تحميل البرومبت: ${error.message}`, 'error');
                document.getElementById('testResults').innerHTML = `
                    <div class="error">
                        <h3>❌ خطأ في تحميل البرومبت</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testVulnerabilityExtraction() {
            log('⚡ بدء اختبار استخراج الثغرات من البرومبت...', 'info');
            
            if (!await initializeBugBounty()) {
                return;
            }

            try {
                const testUrl = 'https://example.com';
                log(`🎯 اختبار استخراج الثغرات للموقع: ${testUrl}`, 'info');
                
                const vulnerabilities = await bugBountyCore.extractAllVulnerabilitiesFromPrompt('', testUrl);
                
                log(`📊 تم استخراج ${vulnerabilities.length} ثغرة من البرومبت`, vulnerabilities.length > 0 ? 'success' : 'warning');
                
                if (vulnerabilities.length > 0) {
                    log('🔍 أول 3 ثغرات مستخرجة:', 'info');
                    vulnerabilities.slice(0, 3).forEach((vuln, index) => {
                        log(`${index + 1}. ${vuln.name || vuln.type || 'ثغرة غير محددة'}`, 'info');
                    });
                }
                
                document.getElementById('testResults').innerHTML += `
                    <div class="${vulnerabilities.length > 0 ? 'success' : 'warning'}">
                        <h3>⚡ نتائج استخراج الثغرات</h3>
                        <p><strong>عدد الثغرات المستخرجة:</strong> ${vulnerabilities.length}</p>
                        <p><strong>الحالة:</strong> ${vulnerabilities.length > 0 ? 'نجح الاستخراج' : 'لم يتم استخراج ثغرات'}</p>
                    </div>
                `;
                
            } catch (error) {
                log(`❌ خطأ في استخراج الثغرات: ${error.message}`, 'error');
                document.getElementById('testResults').innerHTML += `
                    <div class="error">
                        <h3>❌ خطأ في استخراج الثغرات</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            log('🚀 تم تحميل صفحة اختبار البرومبت الديناميكي', 'success');
            log('📋 جاهز لبدء الاختبارات...', 'info');
        });
    </script>
</body>
</html>
