# اختبار نظام تحميل القالب - Bug Bounty System
Write-Host "🛡️ اختبار نظام تحميل القالب - Bug Bounty System v4.0" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

# التحقق من وجود الملفات المطلوبة
$templatePath = "assets\modules\bugbounty\report_template.html"
$corePath = "assets\modules\bugbounty\BugBountyCore.js"

Write-Host "📁 التحقق من وجود الملفات..." -ForegroundColor Yellow

if (Test-Path $templatePath) {
    $templateSize = (Get-Item $templatePath).Length
    Write-Host "✅ ملف القالب موجود: $templatePath ($templateSize بايت)" -ForegroundColor Green
} else {
    Write-Host "❌ ملف القالب غير موجود: $templatePath" -ForegroundColor Red
    exit 1
}

if (Test-Path $corePath) {
    $coreSize = (Get-Item $corePath).Length
    Write-Host "✅ ملف النظام الأساسي موجود: $corePath ($coreSize بايت)" -ForegroundColor Green
} else {
    Write-Host "❌ ملف النظام الأساسي غير موجود: $corePath" -ForegroundColor Red
    exit 1
}

# التحقق من محتوى القالب
Write-Host "`n📋 تحليل محتوى القالب..." -ForegroundColor Yellow
$templateContent = Get-Content $templatePath -Raw -Encoding UTF8

if ($templateContent.Length -gt 0) {
    Write-Host "✅ القالب يحتوي على محتوى ($($templateContent.Length) حرف)" -ForegroundColor Green
    
    # التحقق من المتغيرات المطلوبة
    $requiredVariables = @(
        "{{TARGET_URL}}",
        "{{TOTAL_VULNERABILITIES}}",
        "{{TIMESTAMP}}",
        "{{VULNERABILITIES_HTML}}",
        "{{TESTING_DETAILS}}",
        "{{INTERACTIVE_DIALOGUES}}",
        "{{VISUAL_CHANGES}}",
        "{{PERSISTENT_RESULTS}}"
    )
    
    Write-Host "`n🔍 التحقق من المتغيرات المطلوبة..." -ForegroundColor Yellow
    $missingVariables = @()
    
    foreach ($variable in $requiredVariables) {
        if ($templateContent.Contains($variable)) {
            Write-Host "✅ $variable موجود" -ForegroundColor Green
        } else {
            Write-Host "❌ $variable مفقود" -ForegroundColor Red
            $missingVariables += $variable
        }
    }
    
    if ($missingVariables.Count -eq 0) {
        Write-Host "✅ جميع المتغيرات المطلوبة موجودة في القالب" -ForegroundColor Green
    } else {
        Write-Host "⚠️ يوجد $($missingVariables.Count) متغير مفقود" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ القالب فارغ أو تالف" -ForegroundColor Red
    exit 1
}

# التحقق من دالة preloadTemplate في الكود
Write-Host "`n🔍 التحقق من دالة preloadTemplate..." -ForegroundColor Yellow
$coreContent = Get-Content $corePath -Raw -Encoding UTF8

if ($coreContent.Contains("preloadTemplate")) {
    Write-Host "✅ دالة preloadTemplate موجودة في الكود" -ForegroundColor Green
    
    # البحث عن التحديثات الجديدة
    if ($coreContent.Contains("this.cachedTemplate")) {
        Write-Host "✅ آلية التخزين المؤقت موجودة" -ForegroundColor Green
    } else {
        Write-Host "❌ آلية التخزين المؤقت مفقودة" -ForegroundColor Red
    }
    
    if ($coreContent.Contains("await this.preloadTemplate()")) {
        Write-Host "✅ استدعاء تحميل القالب مسبقاً موجود" -ForegroundColor Green
    } else {
        Write-Host "⚠️ استدعاء تحميل القالب مسبقاً قد يكون مفقود" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ دالة preloadTemplate غير موجودة في الكود" -ForegroundColor Red
}

# اختبار الوصول للخادم المحلي
Write-Host "`n🌐 اختبار الوصول للخادم المحلي..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/assets/modules/bugbounty/report_template.html" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ الخادم المحلي يعمل والقالب متاح عبر HTTP" -ForegroundColor Green
        Write-Host "📊 حجم الاستجابة: $($response.Content.Length) حرف" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ الخادم يستجيب لكن بحالة غير متوقعة: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ فشل في الوصول للخادم المحلي: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 تأكد من تشغيل الخادم بالأمر: python -m http.server 3000" -ForegroundColor Cyan
}

# ملخص النتائج
Write-Host "`n📊 ملخص نتائج الاختبار:" -ForegroundColor Cyan
Write-Host "=" * 40 -ForegroundColor Gray

$issues = @()
if (-not (Test-Path $templatePath)) { $issues += "ملف القالب مفقود" }
if (-not (Test-Path $corePath)) { $issues += "ملف النظام الأساسي مفقود" }
if ($missingVariables.Count -gt 0) { $issues += "$($missingVariables.Count) متغير مفقود في القالب" }
if (-not $coreContent.Contains("preloadTemplate")) { $issues += "دالة preloadTemplate مفقودة" }

if ($issues.Count -eq 0) {
    Write-Host "🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام" -ForegroundColor Green
} else {
    Write-Host "⚠️ يوجد $($issues.Count) مشكلة تحتاج إصلاح:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "  - $issue" -ForegroundColor Red
    }
}

Write-Host "`n🔧 للاختبار الفعلي، افتح: http://localhost:3000/simple_test.html" -ForegroundColor Cyan
Write-Host "📋 أو استخدم: http://localhost:3000/test_template_loading.html للاختبار المتقدم" -ForegroundColor Cyan
