<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استخراج الثغرات من البرومبت</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #3498db;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .success { border-color: #27ae60; background: #f2fdf2; }
        .warning { border-color: #f39c12; background: #fdf9f2; }
        .error { border-color: #e74c3c; background: #fdf2f2; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover { background: #2980b9; }
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .result-box {
            background: #34495e;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار استخراج الثغرات من البرومبت - تشخيص المشكلة</h1>
        
        <div class="test-section">
            <h2>📋 خطوات التشخيص</h2>
            <button onclick="step1_loadPrompt()">1️⃣ تحميل البرومبت</button>
            <button onclick="step2_testExtraction()">2️⃣ اختبار الاستخراج</button>
            <button onclick="step3_testFullFlow()">3️⃣ اختبار التدفق الكامل</button>
            <button onclick="clearAll()">🗑️ مسح الكل</button>
        </div>

        <div class="test-section">
            <h2>📊 سجل التشخيص</h2>
            <div id="diagnosticLog" class="log"></div>
        </div>

        <div class="test-section">
            <h2>📄 نتائج البرومبت</h2>
            <div id="promptResults" class="result-box"></div>
        </div>

        <div class="test-section">
            <h2>🔍 نتائج الاستخراج</h2>
            <div id="extractionResults" class="result-box"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('diagnosticLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colorMap = {
                'info': '#3498db',
                'success': '#27ae60',
                'warning': '#f39c12',
                'error': '#e74c3c'
            };
            
            logElement.innerHTML += `<span style="color: ${colorMap[type] || '#ecf0f1'}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearAll() {
            document.getElementById('diagnosticLog').innerHTML = '';
            document.getElementById('promptResults').innerHTML = '';
            document.getElementById('extractionResults').innerHTML = '';
        }

        async function initializeBugBounty() {
            if (!bugBountyCore) {
                log('🔧 تهيئة نظام Bug Bounty v4...', 'info');
                try {
                    bugBountyCore = new BugBountyCore();
                    log('✅ تم تهيئة النظام بنجاح', 'success');
                    return true;
                } catch (error) {
                    log(`❌ فشل في تهيئة النظام: ${error.message}`, 'error');
                    return false;
                }
            }
            return true;
        }

        async function step1_loadPrompt() {
            log('🔍 الخطوة 1: تحميل البرومبت من prompt_template.txt...', 'info');
            
            if (!await initializeBugBounty()) {
                return;
            }

            try {
                const prompt = await bugBountyCore.loadPromptTemplate();
                
                if (prompt && prompt.length > 0) {
                    log(`✅ تم تحميل البرومبت بنجاح!`, 'success');
                    log(`📏 حجم البرومبت: ${prompt.length} حرف`, 'info');
                    log(`📊 عدد الأسطر: ${prompt.split('\n').length}`, 'info');
                    
                    // عرض معاينة البرومبت
                    const preview = prompt.substring(0, 500);
                    document.getElementById('promptResults').innerHTML = `
                        <h3>✅ البرومبت محمل بنجاح</h3>
                        <p><strong>الحجم:</strong> ${prompt.length} حرف</p>
                        <p><strong>الأسطر:</strong> ${prompt.split('\n').length}</p>
                        <h4>معاينة البداية:</h4>
                        <pre style="background: #2c3e50; color: #ecf0f1; padding: 10px; border-radius: 5px; white-space: pre-wrap;">${preview}...</pre>
                    `;
                    
                    // حفظ البرومبت للاختبارات التالية
                    window.testPrompt = prompt;
                    
                } else {
                    log('❌ البرومبت فارغ أو غير صالح', 'error');
                    document.getElementById('promptResults').innerHTML = `
                        <h3>❌ فشل في تحميل البرومبت</h3>
                        <p>البرومبت فارغ أو غير صالح</p>
                    `;
                }
            } catch (error) {
                log(`❌ خطأ في تحميل البرومبت: ${error.message}`, 'error');
                document.getElementById('promptResults').innerHTML = `
                    <h3>❌ خطأ في تحميل البرومبت</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        async function step2_testExtraction() {
            log('🔍 الخطوة 2: اختبار استخراج الثغرات...', 'info');
            
            if (!await initializeBugBounty()) {
                return;
            }

            if (!window.testPrompt) {
                log('❌ يجب تحميل البرومبت أولاً (الخطوة 1)', 'error');
                return;
            }

            try {
                const testUrl = 'http://testphp.vulnweb.com';
                log(`🎯 اختبار استخراج الثغرات للموقع: ${testUrl}`, 'info');
                
                // اختبار دالة الاستخراج الرئيسية
                log('📋 استدعاء extractAllVulnerabilitiesFromPrompt...', 'info');
                const vulnerabilities = await bugBountyCore.extractAllVulnerabilitiesFromPrompt(window.testPrompt, testUrl);
                
                log(`📊 تم استخراج ${vulnerabilities.length} ثغرة من البرومبت`, vulnerabilities.length > 0 ? 'success' : 'warning');
                
                // عرض النتائج
                let resultsHTML = `
                    <h3>📊 نتائج استخراج الثغرات</h3>
                    <p><strong>عدد الثغرات المستخرجة:</strong> ${vulnerabilities.length}</p>
                `;
                
                if (vulnerabilities.length > 0) {
                    resultsHTML += `<h4>أول 5 ثغرات:</h4><ul>`;
                    vulnerabilities.slice(0, 5).forEach((vuln, index) => {
                        resultsHTML += `<li>${index + 1}. ${vuln.name || vuln.type || 'ثغرة غير محددة'} (${vuln.severity || 'غير محدد'})</li>`;
                    });
                    resultsHTML += `</ul>`;
                    
                    log('🔍 أول 5 ثغرات مستخرجة:', 'info');
                    vulnerabilities.slice(0, 5).forEach((vuln, index) => {
                        log(`${index + 1}. ${vuln.name || vuln.type || 'ثغرة غير محددة'}`, 'info');
                    });
                } else {
                    resultsHTML += `<p style="color: #e74c3c;">⚠️ لم يتم استخراج أي ثغرات - هذا يشير إلى مشكلة في دوال الاستخراج</p>`;
                }
                
                document.getElementById('extractionResults').innerHTML = resultsHTML;
                
            } catch (error) {
                log(`❌ خطأ في استخراج الثغرات: ${error.message}`, 'error');
                document.getElementById('extractionResults').innerHTML = `
                    <h3>❌ خطأ في استخراج الثغرات</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        async function step3_testFullFlow() {
            log('🔍 الخطوة 3: اختبار التدفق الكامل...', 'info');
            
            if (!await initializeBugBounty()) {
                return;
            }

            try {
                const testUrl = 'http://testphp.vulnweb.com';
                log(`🎯 اختبار التدفق الكامل للموقع: ${testUrl}`, 'info');
                
                // محاكاة بيانات الصفحة
                const mockPageData = {
                    url: testUrl,
                    html_content: '<html><body><form><input name="username"><input name="password"></form></body></html>',
                    forms: [{ action: '/login', method: 'POST', inputs: ['username', 'password'] }],
                    links: ['/admin', '/user', '/api'],
                    scripts: ['jquery.js', 'app.js'],
                    headers: { 'server': 'Apache', 'x-powered-by': 'PHP' },
                    cookies: [{ name: 'session', value: 'abc123' }],
                    technologies: ['PHP', 'MySQL', 'Apache']
                };
                
                log('📋 اختبار performLocalComprehensiveAnalysis...', 'info');
                const analysis = await bugBountyCore.performLocalComprehensiveAnalysis(mockPageData, testUrl, window.testPrompt);
                
                log(`📊 التحليل الشامل: ${analysis.vulnerabilities?.length || 0} ثغرة`, 'info');
                
                document.getElementById('extractionResults').innerHTML += `
                    <hr>
                    <h3>🔬 نتائج التحليل الشامل</h3>
                    <p><strong>عدد الثغرات:</strong> ${analysis.vulnerabilities?.length || 0}</p>
                    <p><strong>الحالة:</strong> ${analysis.vulnerabilities?.length > 0 ? 'نجح التحليل' : 'فشل في اكتشاف الثغرات'}</p>
                `;
                
            } catch (error) {
                log(`❌ خطأ في التدفق الكامل: ${error.message}`, 'error');
                document.getElementById('extractionResults').innerHTML += `
                    <hr>
                    <h3>❌ خطأ في التدفق الكامل</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            log('🚀 تم تحميل صفحة تشخيص استخراج الثغرات', 'success');
            log('📋 جاهز لبدء التشخيص...', 'info');
            log('💡 ابدأ بالخطوة 1 لتحميل البرومبت', 'info');
        });
    </script>
</body>
</html>
