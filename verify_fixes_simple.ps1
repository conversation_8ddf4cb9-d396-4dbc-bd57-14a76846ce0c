# Bug Bounty v4.0 Fixes Verification
# Simple PowerShell Script to verify fixes

Write-Host "Starting Bug Bounty v4.0 fixes verification..." -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"

# Check if file exists
if (-not (Test-Path $bugBountyFile)) {
    Write-Host "ERROR: BugBountyCore.js file not found" -ForegroundColor Red
    exit 1
}

Write-Host "Found BugBountyCore.js file" -ForegroundColor Green

# Read file content
$fileContent = Get-Content $bugBountyFile -Raw -Encoding UTF8

Write-Host "`nChecking for generic text removal..." -ForegroundColor Yellow

# List of generic texts that should be removed (in Arabic)
$genericTexts = @(
    "معامل مكتشف",
    "استجابة مكتشفة", 
    "أدلة مكتشفة",
    "تأثير مكتشف",
    "رسائل خطأ مكتشفة"
)

$foundGenericTexts = 0
foreach ($text in $genericTexts) {
    if ($fileContent -match [regex]::Escape($text)) {
        $foundGenericTexts++
        Write-Host "FOUND: $text" -ForegroundColor Red
    } else {
        Write-Host "REMOVED: $text" -ForegroundColor Green
    }
}

Write-Host "`nChecking for new real data functions..." -ForegroundColor Yellow

# List of new functions that should exist
$newFunctions = @(
    "extractParameterFromContext",
    "extractErrorMessagesFromVulnerability", 
    "generateUserPrivacyImpact",
    "generateSystemStabilityImpact",
    "calculateResponseTimeFromVulnerability",
    "calculateRequestSizeFromVulnerability",
    "calculateResponseSizeFromVulnerability", 
    "calculateConfidenceLevelFromVulnerability",
    "estimateAffectedTables"
)

$foundFunctions = 0
foreach ($func in $newFunctions) {
    if ($fileContent -match "$func\s*\(") {
        $foundFunctions++
        Write-Host "FOUND: $func" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $func" -ForegroundColor Red
    }
}

Write-Host "`nChecking Math.random() replacement..." -ForegroundColor Yellow

# Search for Math.random() usage
$mathRandomMatches = [regex]::Matches($fileContent, "Math\.random\(\)")
$criticalRandomUsage = 0

foreach ($match in $mathRandomMatches) {
    $lineStart = $fileContent.LastIndexOf("`n", $match.Index) + 1
    $lineEnd = $fileContent.IndexOf("`n", $match.Index)
    if ($lineEnd -eq -1) { $lineEnd = $fileContent.Length }
    
    $line = $fileContent.Substring($lineStart, $lineEnd - $lineStart)
    
    # Check for critical contexts
    if ($line -match "(confidence|response_time|records|tables|impact)" -and 
        $line -notmatch "(cacheBuster|generateReportId)") {
        $criticalRandomUsage++
    }
}

Write-Host "Total Math.random() usage: $($mathRandomMatches.Count)" -ForegroundColor Cyan
Write-Host "Critical usage: $criticalRandomUsage" -ForegroundColor $(if ($criticalRandomUsage -eq 0) { "Green" } else { "Red" })

Write-Host "`nChecking real data function calls..." -ForegroundColor Yellow

# Check for real data function calls
$realDataCalls = @(
    "this\.extractParameterFromContext\(",
    "this\.generateEvidenceFromVulnerabilityData\(",
    "this\.generateRealImpactFromVulnerability\(",
    "this\.extractErrorMessagesFromVulnerability\(",
    "this\.calculateResponseTimeFromVulnerability\(",
    "this\.calculateConfidenceLevelFromVulnerability\("
)

$foundRealDataCalls = 0
foreach ($call in $realDataCalls) {
    $matches = [regex]::Matches($fileContent, $call)
    $foundRealDataCalls += $matches.Count
    Write-Host "Calls to $call : $($matches.Count)" -ForegroundColor Green
}

Write-Host "`nSUMMARY RESULTS:" -ForegroundColor Cyan
Write-Host "=" * 40 -ForegroundColor Gray

$tests = @{
    "Generic Text Removal" = ($foundGenericTexts -eq 0)
    "New Functions Added" = ($foundFunctions -eq $newFunctions.Count)
    "Math.random() Replaced" = ($criticalRandomUsage -eq 0)
    "Real Data Calls" = ($foundRealDataCalls -gt 0)
}

$passedTests = 0
foreach ($test in $tests.GetEnumerator()) {
    $status = if ($test.Value) { "PASS" } else { "FAIL" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "$status - $($test.Key)" -ForegroundColor $color
    if ($test.Value) { $passedTests++ }
}

Write-Host "`nFinal Result: $passedTests/$($tests.Count) tests passed" -ForegroundColor Cyan

if ($passedTests -eq $tests.Count) {
    Write-Host "`nSUCCESS! All fixes are working correctly." -ForegroundColor Green
    Write-Host "The system now generates real vulnerability-specific content." -ForegroundColor Green
} else {
    Write-Host "`nSome fixes need additional review." -ForegroundColor Yellow
}

Write-Host "`nAdditional Details:" -ForegroundColor Cyan
Write-Host "- Generic texts removed: $($genericTexts.Count - $foundGenericTexts)/$($genericTexts.Count)" -ForegroundColor White
Write-Host "- New functions added: $foundFunctions/$($newFunctions.Count)" -ForegroundColor White
Write-Host "- Real data function calls found: $foundRealDataCalls" -ForegroundColor White
Write-Host "- Critical Math.random() usage: $criticalRandomUsage" -ForegroundColor White

Write-Host "`nVerification completed." -ForegroundColor Green
