<!DOCTYPE html>
<html>
<head>
    <title>اختبار بسيط - Bug Bounty Template Loading</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>اختبار تحميل القالب</h1>
    <div id="output"></div>
    <button onclick="testSystem()">اختبار النظام</button>
    
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }
        
        async function testSystem() {
            try {
                log('🔄 بدء الاختبار...');
                
                if (typeof BugBountyCore === 'undefined') {
                    log('❌ BugBountyCore غير متاح');
                    return;
                }
                
                log('✅ BugBountyCore متاح');
                
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore');
                
                if (typeof core.preloadTemplate === 'function') {
                    log('✅ دالة preloadTemplate متاحة');
                    
                    log('🔄 اختبار تحميل القالب...');
                    await core.preloadTemplate();
                    
                    if (core.cachedTemplate) {
                        log('✅ تم تحميل القالب بنجاح - الطول: ' + core.cachedTemplate.length);
                        
                        // اختبار التصدير
                        log('🔄 اختبار التصدير...');
                        const mockAnalysis = {
                            vulnerabilities: [],
                            total_vulnerabilities: 0,
                            pages_processed: 1
                        };
                        
                        try {
                            const report = await core.generateFinalComprehensiveReport(mockAnalysis, [], 'https://example.com');
                            if (report) {
                                log('✅ تم إنشاء التقرير بنجاح - الطول: ' + report.length);
                            } else {
                                log('❌ فشل في إنشاء التقرير');
                            }
                        } catch (exportError) {
                            log('❌ خطأ في التصدير: ' + exportError.message);
                        }
                    } else {
                        log('❌ فشل في تحميل القالب');
                    }
                } else {
                    log('❌ دالة preloadTemplate غير متاحة');
                }
                
            } catch (error) {
                log('❌ خطأ: ' + error.message);
            }
        }
        
        // اختبار تلقائي عند التحميل
        window.addEventListener('load', () => {
            setTimeout(testSystem, 1000);
        });
    </script>
</body>
</html>
