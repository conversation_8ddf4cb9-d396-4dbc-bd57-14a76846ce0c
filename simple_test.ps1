Write-Host "Bug Bounty Template Loading System Test" -ForegroundColor Cyan

# Check template file
$templatePath = "assets\modules\bugbounty\report_template.html"
if (Test-Path $templatePath) {
    $templateSize = (Get-Item $templatePath).Length
    Write-Host "Template file exists: $templateSize bytes" -ForegroundColor Green
} else {
    Write-Host "Template file missing" -ForegroundColor Red
    exit 1
}

# Check core file
$corePath = "assets\modules\bugbounty\BugBountyCore.js"
if (Test-Path $corePath) {
    $coreSize = (Get-Item $corePath).Length
    Write-Host "Core file exists: $coreSize bytes" -ForegroundColor Green
} else {
    Write-Host "Core file missing" -ForegroundColor Red
    exit 1
}

# Check for preloadTemplate function
$coreContent = Get-Content $corePath -Raw -Encoding UTF8
if ($coreContent.Contains("preloadTemplate")) {
    Write-Host "preloadTemplate function found" -ForegroundColor Green
} else {
    Write-Host "preloadTemplate function missing" -ForegroundColor Red
}

if ($coreContent.Contains("this.cachedTemplate")) {
    Write-Host "Caching mechanism found" -ForegroundColor Green
} else {
    Write-Host "Caching mechanism missing" -ForegroundColor Red
}

# Test server access
Write-Host "Testing server access..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/assets/modules/bugbounty/report_template.html" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "Server accessible, template loads successfully" -ForegroundColor Green
        Write-Host "Response size: $($response.Content.Length) characters" -ForegroundColor Cyan
    }
} catch {
    Write-Host "Server not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed" -ForegroundColor Cyan
