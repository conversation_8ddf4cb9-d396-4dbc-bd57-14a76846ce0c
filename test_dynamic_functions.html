<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الدوال الديناميكية المحسنة - Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .real-data {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        .fallback-data {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .vulnerability-data {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #b3d9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار الدوال الديناميكية المحسنة - Bug Bounty v4.0</h1>
        <p>هذا الاختبار يتحقق من أن الدوال الديناميكية تستخدم البيانات الحقيقية من الثغرات المكتشفة بدلاً من النصوص الثابتة.</p>

        <div class="test-section">
            <h2>📊 بيانات الثغرة التجريبية</h2>
            <div class="vulnerability-data" id="vulnerabilityData">
                سيتم عرض بيانات الثغرة هنا...
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 اختبار الدوال</h2>
            <button onclick="testAllFunctions()">اختبار جميع الدوال</button>
            <button onclick="testWithRealData()">اختبار مع بيانات حقيقية</button>
            <button onclick="testWithoutData()">اختبار بدون بيانات</button>
            <button onclick="clearResults()">مسح النتائج</button>
        </div>

        <div class="test-section">
            <h2>📋 نتائج الاختبار</h2>
            <div id="testResults">
                انقر على "اختبار جميع الدوال" لبدء الاختبار...
            </div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        // إنشاء instance من BugBountyCore
        const bugBountyCore = new BugBountyCore();

        // بيانات ثغرة تجريبية مع بيانات حقيقية
        const testVulnerability = {
            name: "SQL Injection في صفحة البحث",
            type: "SQL Injection",
            category: "Database",
            location: "https://example.com/search?q=test",
            url: "https://example.com/search",
            method: "POST",
            parameter: "search_query",
            vulnerable_parameter: "q",
            payload: "' UNION SELECT username,password FROM users--",
            test_payload: "1' OR '1'='1'--",
            request: "POST /search HTTP/1.1\nHost: example.com\nContent-Type: application/x-www-form-urlencoded\n\nq=' UNION SELECT * FROM users--",
            request_sent: "POST /search HTTP/1.1 - تم إرسال SQL injection payload",
            response: "HTTP/1.1 200 OK - تم إرجاع بيانات المستخدمين من قاعدة البيانات",
            response_received: "HTTP/1.1 200 OK - عرض أسماء المستخدمين وكلمات المرور",
            response_code: "200 OK",
            evidence: "تم استخراج 15 حساب مستخدم مع كلمات المرور المشفرة",
            visual_evidence: "لقطات شاشة تظهر عرض بيانات المستخدمين في صفحة النتائج",
            exploitation_result: {
                poc: {
                    payload: "' UNION SELECT user_id,username,password,email FROM admin_users--",
                    request: "POST /search HTTP/1.1\nContent-Length: 67\n\nq=' UNION SELECT user_id,username,password,email FROM admin_users--",
                    response_snippet: "HTTP/1.1 200 OK\nContent-Type: text/html\n\n<div>admin:$2y$10$hash...</div>"
                },
                response_analysis: "تم الكشف عن 3 حسابات إدارية مع هاش كلمات المرور",
                visual_evidence: "صور توضح عرض بيانات الإدارة في واجهة البحث"
            },
            testing_results: [{
                payload_used: "1' UNION SELECT @@version,user(),database()--",
                evidence: "تم الكشف عن إصدار MySQL 8.0.25 ومعلومات قاعدة البيانات",
                request: "GET /search?q=1' UNION SELECT @@version,user(),database()-- HTTP/1.1",
                response_snippet: "MySQL 8.0.25 | root@localhost | ecommerce_db"
            }],
            screenshots: ["before_exploit.png", "during_exploit.png", "after_exploit.png"]
        };

        // عرض بيانات الثغرة
        function displayVulnerabilityData() {
            const dataDiv = document.getElementById('vulnerabilityData');
            dataDiv.innerHTML = `
                <h3>🎯 ${testVulnerability.name}</h3>
                <p><strong>النوع:</strong> ${testVulnerability.type}</p>
                <p><strong>الموقع:</strong> ${testVulnerability.location}</p>
                <p><strong>المعامل:</strong> ${testVulnerability.parameter}</p>
                <p><strong>Payload:</strong> <code>${testVulnerability.payload}</code></p>
                <p><strong>الدليل:</strong> ${testVulnerability.evidence}</p>
            `;
        }

        // اختبار جميع الدوال
        function testAllFunctions() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>🔄 جاري تشغيل الاختبارات...</h3>';

            setTimeout(() => {
                let results = '<h3>✅ نتائج الاختبار:</h3>';

                // اختبار generateRealPayload
                try {
                    const payloadWithData = bugBountyCore.generateRealPayload(testVulnerability.type, testVulnerability);
                    const payloadWithoutData = bugBountyCore.generateRealPayload(testVulnerability.type);
                    
                    results += `
                        <div class="test-result real-data">
                            <strong>generateRealPayload مع البيانات:</strong><br>
                            ${payloadWithData}
                        </div>
                        <div class="test-result fallback-data">
                            <strong>generateRealPayload بدون البيانات:</strong><br>
                            ${payloadWithoutData}
                        </div>
                    `;
                } catch (error) {
                    results += `<div class="test-result error"><strong>خطأ في generateRealPayload:</strong> ${error.message}</div>`;
                }

                // اختبار generateRealEvidence
                try {
                    const evidenceWithData = bugBountyCore.generateRealEvidence(testVulnerability.type, testVulnerability);
                    const evidenceWithoutData = bugBountyCore.generateRealEvidence(testVulnerability.type);
                    
                    results += `
                        <div class="test-result real-data">
                            <strong>generateRealEvidence مع البيانات:</strong><br>
                            ${evidenceWithData}
                        </div>
                        <div class="test-result fallback-data">
                            <strong>generateRealEvidence بدون البيانات:</strong><br>
                            ${evidenceWithoutData}
                        </div>
                    `;
                } catch (error) {
                    results += `<div class="test-result error"><strong>خطأ في generateRealEvidence:</strong> ${error.message}</div>`;
                }

                // اختبار generateRealResponse
                try {
                    const responseWithData = bugBountyCore.generateRealResponse(testVulnerability.type, testVulnerability);
                    const responseWithoutData = bugBountyCore.generateRealResponse(testVulnerability.type);
                    
                    results += `
                        <div class="test-result real-data">
                            <strong>generateRealResponse مع البيانات:</strong><br>
                            ${responseWithData}
                        </div>
                        <div class="test-result fallback-data">
                            <strong>generateRealResponse بدون البيانات:</strong><br>
                            ${responseWithoutData}
                        </div>
                    `;
                } catch (error) {
                    results += `<div class="test-result error"><strong>خطأ في generateRealResponse:</strong> ${error.message}</div>`;
                }

                // اختبار generateRealRequest
                try {
                    const requestWithData = bugBountyCore.generateRealRequest(testVulnerability.type, testVulnerability);
                    const requestWithoutData = bugBountyCore.generateRealRequest(testVulnerability.type);
                    
                    results += `
                        <div class="test-result real-data">
                            <strong>generateRealRequest مع البيانات:</strong><br>
                            ${requestWithData}
                        </div>
                        <div class="test-result fallback-data">
                            <strong>generateRealRequest بدون البيانات:</strong><br>
                            ${requestWithoutData}
                        </div>
                    `;
                } catch (error) {
                    results += `<div class="test-result error"><strong>خطأ في generateRealRequest:</strong> ${error.message}</div>`;
                }

                // اختبار generateRealParameter
                try {
                    const parameterWithData = bugBountyCore.generateRealParameter(testVulnerability);
                    const parameterWithoutData = bugBountyCore.generateRealParameter();
                    
                    results += `
                        <div class="test-result real-data">
                            <strong>generateRealParameter مع البيانات:</strong><br>
                            ${parameterWithData}
                        </div>
                        <div class="test-result fallback-data">
                            <strong>generateRealParameter بدون البيانات:</strong><br>
                            ${parameterWithoutData}
                        </div>
                    `;
                } catch (error) {
                    results += `<div class="test-result error"><strong>خطأ في generateRealParameter:</strong> ${error.message}</div>`;
                }

                // اختبار generateRealExploitationResult
                try {
                    const exploitWithData = bugBountyCore.generateRealExploitationResult(testVulnerability.type, testVulnerability);
                    const exploitWithoutData = bugBountyCore.generateRealExploitationResult(testVulnerability.type);
                    
                    results += `
                        <div class="test-result real-data">
                            <strong>generateRealExploitationResult مع البيانات:</strong><br>
                            ${JSON.stringify(exploitWithData, null, 2)}
                        </div>
                        <div class="test-result fallback-data">
                            <strong>generateRealExploitationResult بدون البيانات:</strong><br>
                            ${JSON.stringify(exploitWithoutData, null, 2)}
                        </div>
                    `;
                } catch (error) {
                    results += `<div class="test-result error"><strong>خطأ في generateRealExploitationResult:</strong> ${error.message}</div>`;
                }

                resultsDiv.innerHTML = results;
            }, 500);
        }

        function testWithRealData() {
            testAllFunctions();
        }

        function testWithoutData() {
            // اختبار الدوال بدون تمرير بيانات الثغرة
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>🔄 اختبار بدون بيانات...</h3>';

            setTimeout(() => {
                let results = '<h3>⚠️ نتائج الاختبار بدون بيانات (fallback):</h3>';

                try {
                    results += `<div class="test-result fallback-data"><strong>Payload:</strong> ${bugBountyCore.generateRealPayload('SQL Injection')}</div>`;
                    results += `<div class="test-result fallback-data"><strong>Evidence:</strong> ${bugBountyCore.generateRealEvidence('SQL Injection')}</div>`;
                    results += `<div class="test-result fallback-data"><strong>Response:</strong> ${bugBountyCore.generateRealResponse('SQL Injection')}</div>`;
                    results += `<div class="test-result fallback-data"><strong>Request:</strong> ${bugBountyCore.generateRealRequest('SQL Injection')}</div>`;
                    results += `<div class="test-result fallback-data"><strong>Parameter:</strong> ${bugBountyCore.generateRealParameter()}</div>`;
                } catch (error) {
                    results += `<div class="test-result error"><strong>خطأ:</strong> ${error.message}</div>`;
                }

                resultsDiv.innerHTML = results;
            }, 500);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = 'انقر على "اختبار جميع الدوال" لبدء الاختبار...';
        }

        // عرض بيانات الثغرة عند تحميل الصفحة
        window.onload = function() {
            displayVulnerabilityData();
        };
    </script>
</body>
</html>
