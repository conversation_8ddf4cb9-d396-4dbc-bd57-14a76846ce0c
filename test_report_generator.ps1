Write-Host 'إنشاء تقرير تجريبي لاختبار النظام...' -ForegroundColor Green

# قراءة القالب
$templatePath = 'assets\modules\bugbounty\report_template.html'
$template = Get-Content $templatePath -Raw -Encoding UTF8

# بيانات تجريبية
$testData = @{
    'TARGET_URL' = 'https://example.com'
    'TOTAL_VULNERABILITIES' = '3'
    'SECURITY_LEVEL' = 'متوسط'
    'RISK_SCORE' = '75'
    'HIGHEST_SEVERITY' = 'عالية'
    'IMAGES_COUNT' = '5'
    'VULNERABILITIES_CONTENT' = '<div class="vuln-item"><h3>XSS في نموذج البحث</h3><p>تم اكتشاف ثغرة XSS في حقل البحث</p></div>'
    'TESTING_DETAILS' = '<p>تم إجراء اختبارات شاملة باستخدام payloads متقدمة</p>'
    'INTERACTIVE_DIALOGUES' = '<p>تم إجراء حوارات تفاعلية مع النظام</p>'
    'VISUAL_CHANGES' = '<p>تم رصد تغيرات بصرية أثناء الاختبار</p>'
    'PERSISTENT_RESULTS' = '<p>تم حفظ النتائج بشكل مستمر</p>'
    'IMPACT_VISUALIZATIONS' = '<p>تم توثيق التأثيرات بصرياً</p>'
    'RECOMMENDATIONS_CONTENT' = '<p>يُنصح بتطبيق التحديثات الأمنية</p>'
    'TIMESTAMP' = (Get-Date).ToString('yyyy-MM-dd HH:mm:ss')
    'DATE' = (Get-Date).ToString('yyyy-MM-dd')
}

# استبدال المتغيرات
$finalReport = $template
foreach ($key in $testData.Keys) {
    $pattern = '{{' + $key + '}}'
    $finalReport = $finalReport -replace [regex]::Escape($pattern), $testData[$key]
}

# حفظ التقرير التجريبي
$outputPath = 'test_report_powershell.html'
$finalReport | Out-File -FilePath $outputPath -Encoding UTF8

Write-Host "✅ تم إنشاء التقرير التجريبي: $outputPath" -ForegroundColor Green
Write-Host "📏 طول التقرير النهائي: $($finalReport.Length) حرف" -ForegroundColor Yellow
Write-Host '🔍 فحص وجود المتغيرات غير المستبدلة...' -ForegroundColor Cyan

# فحص المتغيرات غير المستبدلة
$unreplaced = [regex]::Matches($finalReport, '{{.*?}}')
if ($unreplaced.Count -gt 0) {
    Write-Host "⚠️ متغيرات غير مستبدلة: $($unreplaced.Count)" -ForegroundColor Yellow
    $unreplaced | ForEach-Object { Write-Host "   - $($_.Value)" -ForegroundColor Red }
} else {
    Write-Host '✅ تم استبدال جميع المتغيرات بنجاح' -ForegroundColor Green
}

Write-Host '🌐 فتح التقرير في المتصفح...' -ForegroundColor Cyan
Start-Process $outputPath
