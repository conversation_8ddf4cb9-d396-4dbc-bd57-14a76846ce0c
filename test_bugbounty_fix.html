<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .result {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
        .success { border-left: 5px solid #27ae60; }
        .error { border-left: 5px solid #e74c3c; }
        .warning { border-left: 5px solid #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح Bug Bounty v4.0</h1>
        <p>اختبار إصلاح خطأ "vuln is not defined" في النظام الشامل</p>
        
        <button class="test-button" onclick="testBugBountySystem()">🚀 اختبار النظام الشامل</button>
        <button class="test-button" onclick="testPersistentSystem()">🔄 اختبار النظام المثابر</button>
        <button class="test-button" onclick="testVulnerabilityExtraction()">📋 اختبار استخراج الثغرات</button>
        
        <div id="results"></div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        
        // تهيئة النظام
        async function initializeBugBounty() {
            try {
                bugBountyCore = new BugBountyCore();
                console.log('✅ تم تهيئة Bug Bounty Core بنجاح');
                return true;
            } catch (error) {
                console.error('❌ فشل في تهيئة Bug Bounty Core:', error);
                return false;
            }
        }
        
        // اختبار النظام الشامل
        async function testBugBountySystem() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">🔄 جاري اختبار النظام الشامل...</div>';
            
            try {
                if (!bugBountyCore) {
                    const initialized = await initializeBugBounty();
                    if (!initialized) {
                        throw new Error('فشل في تهيئة النظام');
                    }
                }
                
                // اختبار الدالة التي كانت تسبب الخطأ
                const testUrl = 'http://testphp.vulnweb.com';
                const testPrompt = 'اختبار البرومبت الشامل للثغرات';
                
                console.log('🔍 اختبار performLocalComprehensiveAnalysis...');
                const result = await bugBountyCore.performLocalComprehensiveAnalysis({}, testUrl, testPrompt);
                
                resultsDiv.innerHTML = `
                    <div class="result success">
                        ✅ نجح اختبار النظام الشامل!
                        
                        📊 النتائج:
                        - عدد الثغرات المكتشفة: ${result.length}
                        - لا توجد أخطاء "vuln is not defined"
                        - النظام يعمل بشكل طبيعي
                        
                        🎯 الثغرات المكتشفة:
                        ${result.map((v, i) => `${i+1}. ${v.name} (${v.severity})`).join('\n')}
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        ❌ فشل اختبار النظام الشامل:
                        
                        خطأ: ${error.message}
                        
                        ${error.stack ? 'Stack Trace:\n' + error.stack : ''}
                    </div>
                `;
            }
        }
        
        // اختبار النظام المثابر
        async function testPersistentSystem() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">🔄 جاري اختبار النظام المثابر...</div>';
            
            try {
                if (!bugBountyCore) {
                    const initialized = await initializeBugBounty();
                    if (!initialized) {
                        throw new Error('فشل في تهيئة النظام');
                    }
                }
                
                const testUrl = 'http://testphp.vulnweb.com';
                const testPrompt = 'اختبار البرومبت للنظام المثابر';
                
                console.log('🔄 اختبار performPersistentVulnerabilityScanning...');
                const result = await bugBountyCore.performPersistentVulnerabilityScanning(testUrl, testPrompt);
                
                resultsDiv.innerHTML = `
                    <div class="result success">
                        ✅ نجح اختبار النظام المثابر!
                        
                        📊 النتائج:
                        - عدد الثغرات المكتشفة: ${result.length}
                        - لا توجد أخطاء "vuln is not defined"
                        - النظام المثابر يعمل بشكل طبيعي
                        
                        🎯 الثغرات المكتشفة:
                        ${result.map((v, i) => `${i+1}. ${v.name} (${v.severity})`).join('\n')}
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        ❌ فشل اختبار النظام المثابر:
                        
                        خطأ: ${error.message}
                        
                        ${error.stack ? 'Stack Trace:\n' + error.stack : ''}
                    </div>
                `;
            }
        }
        
        // اختبار استخراج الثغرات
        async function testVulnerabilityExtraction() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">🔄 جاري اختبار استخراج الثغرات...</div>';
            
            try {
                if (!bugBountyCore) {
                    const initialized = await initializeBugBounty();
                    if (!initialized) {
                        throw new Error('فشل في تهيئة النظام');
                    }
                }
                
                const testPrompt = 'اختبار استخراج الثغرات من البرومبت';
                const testUrl = 'http://testphp.vulnweb.com';
                
                console.log('📋 اختبار extractAllVulnerabilitiesFromPrompt...');
                const result = await bugBountyCore.extractAllVulnerabilitiesFromPrompt(testPrompt, testUrl);
                
                resultsDiv.innerHTML = `
                    <div class="result success">
                        ✅ نجح اختبار استخراج الثغرات!
                        
                        📊 النتائج:
                        - عدد الثغرات المستخرجة: ${result.length}
                        - لا توجد أخطاء "vuln is not defined"
                        - استخراج الثغرات يعمل بشكل طبيعي
                        
                        🎯 الثغرات المستخرجة:
                        ${result.map((v, i) => `${i+1}. ${v.name} (${v.severity})`).join('\n')}
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        ❌ فشل اختبار استخراج الثغرات:
                        
                        خطأ: ${error.message}
                        
                        ${error.stack ? 'Stack Trace:\n' + error.stack : ''}
                    </div>
                `;
            }
        }
        
        // تهيئة تلقائية عند تحميل الصفحة
        window.addEventListener('load', async () => {
            console.log('🚀 بدء تهيئة نظام Bug Bounty...');
            await initializeBugBounty();
        });
    </script>
</body>
</html>
