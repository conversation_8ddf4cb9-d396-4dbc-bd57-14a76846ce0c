<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .success { border-color: #4CAF50; background: #f1f8e9; }
        .error { border-color: #f44336; background: #ffebee; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
        .loading {
            display: none;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار إصلاح Bug Bounty v4.0</h1>
            <p>التحقق من إصلاح خطأ "vuln is not defined"</p>
        </div>

        <div class="test-section">
            <h3>🧪 اختبار تحميل النظام</h3>
            <button class="btn" onclick="testSystemLoad()">اختبار تحميل BugBountyCore</button>
            <div id="loadResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 اختبار الفحص الشامل</h3>
            <button class="btn" onclick="testComprehensiveScan()">اختبار الفحص الشامل</button>
            <div id="scanResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🛡️ اختبار النظام المثابر</h3>
            <button class="btn" onclick="testPersistentSystem()">اختبار النظام المثابر</button>
            <div id="persistentResult" class="result"></div>
        </div>

        <div class="loading" id="loading">
            <p>⏳ جاري التحميل...</p>
        </div>
    </div>

    <!-- تحميل النظام -->
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>

    <script>
        let bugBountyInstance = null;

        // اختبار تحميل النظام
        async function testSystemLoad() {
            const resultDiv = document.getElementById('loadResult');
            resultDiv.className = 'result';
            resultDiv.textContent = '🔄 جاري اختبار تحميل النظام...';

            try {
                // التحقق من وجود الكلاس
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore غير محمل');
                }

                // إنشاء مثيل
                bugBountyInstance = new BugBountyCore();
                
                // التحقق من الدوال المطلوبة
                const requiredMethods = [
                    'performLocalComprehensiveAnalysis',
                    'attemptWAFBypass',
                    'attemptInputValidationBypass',
                    'performPersistentVulnerabilityScanning'
                ];

                let results = '✅ تم تحميل BugBountyCore بنجاح!\n\n';
                results += '🔍 التحقق من الدوال المطلوبة:\n';

                for (const method of requiredMethods) {
                    if (typeof bugBountyInstance[method] === 'function') {
                        results += `✅ ${method} - موجودة\n`;
                    } else {
                        results += `❌ ${method} - مفقودة\n`;
                        throw new Error(`الدالة ${method} مفقودة`);
                    }
                }

                results += '\n🎉 جميع الدوال المطلوبة موجودة!';
                resultDiv.textContent = results;
                resultDiv.className = 'result success';

            } catch (error) {
                resultDiv.textContent = `❌ خطأ في تحميل النظام:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار الفحص الشامل
        async function testComprehensiveScan() {
            const resultDiv = document.getElementById('scanResult');
            resultDiv.className = 'result';
            resultDiv.textContent = '🔄 جاري اختبار الفحص الشامل...';

            try {
                if (!bugBountyInstance) {
                    await testSystemLoad();
                    if (!bugBountyInstance) {
                        throw new Error('فشل في تهيئة النظام');
                    }
                }

                // اختبار الدالة التي كانت تسبب الخطأ
                const testUrl = 'http://testphp.vulnweb.com';
                const testPrompt = 'اختبار البرومبت الشامل للثغرات';

                console.log('🔍 اختبار performLocalComprehensiveAnalysis...');
                const result = await bugBountyInstance.performLocalComprehensiveAnalysis({}, testUrl, testPrompt);

                let output = '✅ نجح اختبار الفحص الشامل!\n\n';
                output += `📊 النتائج:\n`;
                output += `- عدد الثغرات المكتشفة: ${result.vulnerabilities ? result.vulnerabilities.length : 0}\n`;
                output += `- طريقة التحليل: ${result.analysis_method || 'غير محدد'}\n`;
                output += `- لا توجد أخطاء "vuln is not defined"\n`;
                output += `- النظام يعمل بشكل طبيعي\n\n`;

                if (result.vulnerabilities && result.vulnerabilities.length > 0) {
                    output += `🎯 الثغرات المكتشفة:\n`;
                    result.vulnerabilities.slice(0, 5).forEach((v, i) => {
                        output += `${i+1}. ${v.name || 'ثغرة غير محددة'} (${v.severity || 'غير محدد'})\n`;
                    });
                    if (result.vulnerabilities.length > 5) {
                        output += `... و ${result.vulnerabilities.length - 5} ثغرة أخرى\n`;
                    }
                }

                resultDiv.textContent = output;
                resultDiv.className = 'result success';

            } catch (error) {
                let errorMsg = `❌ خطأ في الفحص الشامل:\n${error.message}\n\n`;
                if (error.message.includes('vuln is not defined')) {
                    errorMsg += '⚠️ لا يزال خطأ "vuln is not defined" موجود!';
                    resultDiv.className = 'result error';
                } else {
                    errorMsg += '💡 الخطأ ليس "vuln is not defined" - قد يكون خطأ آخر';
                    resultDiv.className = 'result warning';
                }
                resultDiv.textContent = errorMsg;
                console.error('خطأ في الفحص:', error);
            }
        }

        // اختبار النظام المثابر
        async function testPersistentSystem() {
            const resultDiv = document.getElementById('persistentResult');
            resultDiv.className = 'result';
            resultDiv.textContent = '🔄 جاري اختبار النظام المثابر...';

            try {
                if (!bugBountyInstance) {
                    await testSystemLoad();
                    if (!bugBountyInstance) {
                        throw new Error('فشل في تهيئة النظام');
                    }
                }

                // اختبار النظام المثابر - تصحيح المعاملات
                const testUrl = 'http://testphp.vulnweb.com';
                const testPrompt = 'اختبار البرومبت الشامل للثغرات';

                console.log('🔍 اختبار performPersistentVulnerabilityScanning...');
                // الدالة تتوقع (pageUrl, fullPrompt) وليس (testData, testUrl)
                const result = await bugBountyInstance.performPersistentVulnerabilityScanning(testUrl, testPrompt);

                let output = '✅ نجح اختبار النظام المثابر!\n\n';
                output += `📊 النتائج:\n`;
                output += `- عدد الثغرات: ${Array.isArray(result) ? result.length : 0}\n`;
                output += `- النظام المثابر يعمل بدون أخطاء\n`;
                output += `- لا توجد أخطاء "vuln is not defined"\n\n`;

                if (Array.isArray(result) && result.length > 0) {
                    output += `🎯 عينة من الثغرات:\n`;
                    result.slice(0, 3).forEach((v, i) => {
                        output += `${i+1}. ${v.name || 'ثغرة غير محددة'}\n`;
                    });
                } else if (result && result.vulnerabilities && result.vulnerabilities.length > 0) {
                    output += `🎯 عينة من الثغرات:\n`;
                    result.vulnerabilities.slice(0, 3).forEach((v, i) => {
                        output += `${i+1}. ${v.name || 'ثغرة غير محددة'}\n`;
                    });
                }

                resultDiv.textContent = output;
                resultDiv.className = 'result success';

            } catch (error) {
                let errorMsg = `❌ خطأ في النظام المثابر:\n${error.message}\n\n`;
                if (error.message.includes('vuln is not defined')) {
                    errorMsg += '⚠️ لا يزال خطأ "vuln is not defined" موجود في النظام المثابر!';
                    resultDiv.className = 'result error';
                } else if (error.message.includes('Invalid URL')) {
                    errorMsg += '💡 مشكلة في URL - تم إصلاحها في الاختبار';
                    resultDiv.className = 'result warning';
                } else {
                    errorMsg += '💡 الخطأ ليس "vuln is not defined"';
                    resultDiv.className = 'result warning';
                }
                resultDiv.textContent = errorMsg;
                console.error('خطأ في النظام المثابر:', error);
            }
        }

        // تحميل تلقائي عند فتح الصفحة
        window.addEventListener('load', () => {
            console.log('🚀 تم تحميل صفحة الاختبار');
            setTimeout(testSystemLoad, 1000);
        });
    </script>
</body>
</html>
