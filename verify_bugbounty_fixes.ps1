# تحقق من إصلاحات Bug Bounty v4.0
# PowerShell Script للتحقق من أن النظام يولد محتوى حقيقي بدلاً من النصوص العامة

Write-Host "🔧 بدء التحقق من إصلاحات Bug Bounty v4.0..." -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"
$testResults = @{}

# التحقق من وجود الملف
if (-not (Test-Path $bugBountyFile)) {
    Write-Host "❌ خطأ: لم يتم العثور على ملف BugBountyCore.js" -ForegroundColor Red
    exit 1
}

Write-Host "✅ تم العثور على ملف BugBountyCore.js" -ForegroundColor Green

# قراءة محتوى الملف
$fileContent = Get-Content $bugBountyFile -Raw -Encoding UTF8

Write-Host "`n📋 التحقق من حذف النصوص العامة..." -ForegroundColor Yellow

# قائمة النصوص العامة التي يجب أن تكون محذوفة
$genericTexts = @(
    "معامل مكتشف",
    "payload غير محدد", 
    "استجابة مكتشفة",
    "أدلة مكتشفة",
    "تأثير مكتشف",
    "رسائل خطأ مكتشفة",
    "بيانات حساسة مكشوفة",
    "تأثير خطير على أمان النظام"
)

$foundGenericTexts = @()
foreach ($text in $genericTexts) {
    if ($fileContent -match [regex]::Escape($text)) {
        $foundGenericTexts += $text
        Write-Host "❌ لا يزال موجود: $text" -ForegroundColor Red
    } else {
        Write-Host "✅ تم حذفه: $text" -ForegroundColor Green
    }
}

$testResults["GenericTextRemoval"] = $foundGenericTexts.Count -eq 0

Write-Host "`n🎯 التحقق من الدوال الجديدة للبيانات الحقيقية..." -ForegroundColor Yellow

# قائمة الدوال الجديدة التي يجب أن تكون موجودة
$newFunctions = @(
    "extractParameterFromContext",
    "extractErrorMessagesFromVulnerability", 
    "generateUserPrivacyImpact",
    "generateSystemStabilityImpact",
    "calculateResponseTimeFromVulnerability",
    "calculateRequestSizeFromVulnerability",
    "calculateResponseSizeFromVulnerability", 
    "calculateConfidenceLevelFromVulnerability",
    "estimateAffectedTables"
)

$missingFunctions = @()
foreach ($func in $newFunctions) {
    if ($fileContent -match "$func\s*\(") {
        Write-Host "✅ موجودة: $func" -ForegroundColor Green
    } else {
        $missingFunctions += $func
        Write-Host "❌ مفقودة: $func" -ForegroundColor Red
    }
}

$testResults["NewFunctions"] = $missingFunctions.Count -eq 0

Write-Host "`n🔢 التحقق من استبدال Math.random()..." -ForegroundColor Yellow

# البحث عن استخدامات Math.random() المتبقية في السياقات الحرجة
$mathRandomMatches = [regex]::Matches($fileContent, "Math\.random\(\)")
$criticalRandomUsage = 0

foreach ($match in $mathRandomMatches) {
    $lineStart = $fileContent.LastIndexOf("`n", $match.Index) + 1
    $lineEnd = $fileContent.IndexOf("`n", $match.Index)
    if ($lineEnd -eq -1) { $lineEnd = $fileContent.Length }
    
    $line = $fileContent.Substring($lineStart, $lineEnd - $lineStart)
    
    # تحقق من السياقات الحرجة
    if ($line -match "(confidence|response_time|records|tables|impact)" -and 
        $line -notmatch "(cacheBuster|generateReportId)") {
        $criticalRandomUsage++
        Write-Host "❌ استخدام Math.random() في سياق حرج: $($line.Trim())" -ForegroundColor Red
    }
}

Write-Host "📊 إجمالي استخدامات Math.random(): $($mathRandomMatches.Count)" -ForegroundColor Cyan
Write-Host "🔴 استخدامات حرجة: $criticalRandomUsage" -ForegroundColor $(if ($criticalRandomUsage -eq 0) { "Green" } else { "Red" })

$testResults["MathRandomReplacement"] = $criticalRandomUsage -eq 0

Write-Host "`n🔍 التحقق من استدعاءات الدوال الحقيقية..." -ForegroundColor Yellow

# التحقق من استبدال النصوص العامة بدوال حقيقية
$realDataCalls = @(
    "this\.extractParameterFromContext\(",
    "this\.generateEvidenceFromVulnerabilityData\(",
    "this\.generateRealImpactFromVulnerability\(",
    "this\.extractErrorMessagesFromVulnerability\(",
    "this\.calculateResponseTimeFromVulnerability\(",
    "this\.calculateConfidenceLevelFromVulnerability\("
)

$foundRealDataCalls = 0
foreach ($call in $realDataCalls) {
    $matches = [regex]::Matches($fileContent, $call)
    $foundRealDataCalls += $matches.Count
    Write-Host "✅ استدعاءات $call : $($matches.Count)" -ForegroundColor Green
}

$testResults["RealDataCalls"] = $foundRealDataCalls -gt 0

Write-Host "`n📈 ملخص النتائج:" -ForegroundColor Cyan
Write-Host "=" * 40 -ForegroundColor Gray

$totalTests = $testResults.Count
$passedTests = ($testResults.Values | Where-Object { $_ -eq $true }).Count

foreach ($test in $testResults.GetEnumerator()) {
    $status = if ($test.Value) { "✅ نجح" } else { "❌ فشل" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "$status - $($test.Key)" -ForegroundColor $color
}

Write-Host "`n📊 النتيجة النهائية: $passedTests/$totalTests اختبارات نجحت" -ForegroundColor Cyan

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 تم بنجاح! جميع الإصلاحات تعمل بشكل صحيح." -ForegroundColor Green
    Write-Host "النظام يولد الآن محتوى حقيقي مخصص للثغرات بدلاً من النصوص العامة." -ForegroundColor Green
} else {
    Write-Host "`n⚠️ تحتاج بعض الإصلاحات إلى مراجعة إضافية." -ForegroundColor Yellow
}

Write-Host "`n🔧 تفاصيل إضافية:" -ForegroundColor Cyan
Write-Host "- تم حذف $($genericTexts.Count - $foundGenericTexts.Count) نص عام من أصل $($genericTexts.Count)" -ForegroundColor White
Write-Host "- تم إضافة $($newFunctions.Count - $missingFunctions.Count) دالة جديدة من أصل $($newFunctions.Count)" -ForegroundColor White
Write-Host "- تم العثور على $foundRealDataCalls استدعاء للدوال الحقيقية" -ForegroundColor White
Write-Host "- تم تقليل الاستخدامات الحرجة لـ Math.random() إلى $criticalRandomUsage" -ForegroundColor White

Write-Host "`n✅ انتهى التحقق من الإصلاحات." -ForegroundColor Green
