# Bug Bounty Template Loading System Test
Write-Host "Bug Bounty Template Loading System Test v4.0" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

# Check required files
$templatePath = "assets\modules\bugbounty\report_template.html"
$corePath = "assets\modules\bugbounty\BugBountyCore.js"

Write-Host "Checking required files..." -ForegroundColor Yellow

if (Test-Path $templatePath) {
    $templateSize = (Get-Item $templatePath).Length
    Write-Host "✅ Template file exists: $templatePath ($templateSize bytes)" -ForegroundColor Green
} else {
    Write-Host "❌ Template file missing: $templatePath" -ForegroundColor Red
    exit 1
}

if (Test-Path $corePath) {
    $coreSize = (Get-Item $corePath).Length
    Write-Host "✅ Core system file exists: $corePath ($coreSize bytes)" -ForegroundColor Green
} else {
    Write-Host "❌ Core system file missing: $corePath" -ForegroundColor Red
    exit 1
}

# Analyze template content
Write-Host "`nAnalyzing template content..." -ForegroundColor Yellow
$templateContent = Get-Content $templatePath -Raw -Encoding UTF8

if ($templateContent.Length -gt 0) {
    Write-Host "✅ Template contains content ($($templateContent.Length) characters)" -ForegroundColor Green
    
    # Check required variables
    $requiredVariables = @(
        "{{TARGET_URL}}",
        "{{TOTAL_VULNERABILITIES}}",
        "{{TIMESTAMP}}",
        "{{VULNERABILITIES_HTML}}",
        "{{TESTING_DETAILS}}",
        "{{INTERACTIVE_DIALOGUES}}",
        "{{VISUAL_CHANGES}}",
        "{{PERSISTENT_RESULTS}}"
    )
    
    Write-Host "`nChecking required variables..." -ForegroundColor Yellow
    $missingVariables = @()
    
    foreach ($variable in $requiredVariables) {
        if ($templateContent.Contains($variable)) {
            Write-Host "✅ $variable found" -ForegroundColor Green
        } else {
            Write-Host "❌ $variable missing" -ForegroundColor Red
            $missingVariables += $variable
        }
    }
    
    if ($missingVariables.Count -eq 0) {
        Write-Host "✅ All required variables found in template" -ForegroundColor Green
    } else {
        Write-Host "⚠️ $($missingVariables.Count) variables missing" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Template is empty or corrupted" -ForegroundColor Red
    exit 1
}

# Check preloadTemplate function in code
Write-Host "`nChecking preloadTemplate function..." -ForegroundColor Yellow
$coreContent = Get-Content $corePath -Raw -Encoding UTF8

if ($coreContent.Contains("preloadTemplate")) {
    Write-Host "✅ preloadTemplate function found in code" -ForegroundColor Green
    
    # Check for new updates
    if ($coreContent.Contains("this.cachedTemplate")) {
        Write-Host "✅ Caching mechanism found" -ForegroundColor Green
    } else {
        Write-Host "❌ Caching mechanism missing" -ForegroundColor Red
    }
    
    if ($coreContent.Contains("await this.preloadTemplate()")) {
        Write-Host "✅ Preload template call found" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Preload template call may be missing" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ preloadTemplate function not found in code" -ForegroundColor Red
}

# Test local server access
Write-Host "`nTesting local server access..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/assets/modules/bugbounty/report_template.html" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Local server working and template accessible via HTTP" -ForegroundColor Green
        Write-Host "📊 Response size: $($response.Content.Length) characters" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Server responds but with unexpected status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Failed to access local server: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure server is running with: python -m http.server 3000" -ForegroundColor Cyan
}

# Test results summary
Write-Host "`nTest Results Summary:" -ForegroundColor Cyan
Write-Host "=" * 40 -ForegroundColor Gray

$issues = @()
if (-not (Test-Path $templatePath)) { $issues += "Template file missing" }
if (-not (Test-Path $corePath)) { $issues += "Core system file missing" }
if ($missingVariables.Count -gt 0) { $issues += "$($missingVariables.Count) variables missing in template" }
if (-not $coreContent.Contains("preloadTemplate")) { $issues += "preloadTemplate function missing" }

if ($issues.Count -eq 0) {
    Write-Host "All tests passed! System ready for use" -ForegroundColor Green
} else {
    Write-Host "Found $($issues.Count) issues that need fixing:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "  - $issue" -ForegroundColor Red
    }
}

Write-Host "`nFor live testing, open: http://localhost:3000/simple_test.html" -ForegroundColor Cyan
Write-Host "Or use: http://localhost:3000/test_template_loading.html for advanced testing" -ForegroundColor Cyan
