<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل القالب - Bug Bounty System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }
        .log-area {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ اختبار تحميل القالب - Bug Bounty System v4.0</h1>
        
        <div class="test-section">
            <h3>📋 اختبار تحميل القالب المسبق</h3>
            <p>هذا الاختبار يتحقق من آلية تحميل القالب الجديدة مع التخزين المؤقت في الذاكرة</p>
            
            <button class="test-button" onclick="testPreloadTemplate()">🔄 اختبار تحميل القالب مسبقاً</button>
            <button class="test-button" onclick="testCachedTemplate()">💾 اختبار القالب المحفوظ</button>
            <button class="test-button" onclick="testExportWithCache()">📤 اختبار التصدير مع التخزين المؤقت</button>
            <button class="test-button" onclick="clearLogs()">🧹 مسح السجلات</button>
        </div>

        <div class="test-section">
            <h3>📊 سجل الاختبار</h3>
            <div id="logArea" class="log-area">جاهز للاختبار...\n</div>
        </div>

        <div class="test-section">
            <h3>📈 حالة النظام</h3>
            <div id="systemStatus">جاري فحص النظام...</div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore = null;
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type;
            logArea.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('logArea').innerHTML = 'تم مسح السجلات...\n';
        }

        async function initializeSystem() {
            try {
                log('🔄 تهيئة نظام Bug Bounty...', 'info');
                
                if (typeof BugBountyCore !== 'undefined') {
                    bugBountyCore = new BugBountyCore();
                    log('✅ تم إنشاء مثيل BugBountyCore بنجاح', 'success');
                    
                    // التحقق من وجود دالة preloadTemplate
                    if (typeof bugBountyCore.preloadTemplate === 'function') {
                        log('✅ دالة preloadTemplate متاحة', 'success');
                    } else {
                        log('❌ دالة preloadTemplate غير متاحة', 'error');
                    }
                    
                    updateSystemStatus();
                } else {
                    log('❌ BugBountyCore غير متاح', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في تهيئة النظام: ${error.message}`, 'error');
            }
        }

        async function testPreloadTemplate() {
            if (!bugBountyCore) {
                log('❌ النظام غير مهيأ', 'error');
                return;
            }

            try {
                log('🔄 بدء اختبار تحميل القالب مسبقاً...', 'info');
                
                // مسح القالب المحفوظ للاختبار
                bugBountyCore.cachedTemplate = null;
                log('🧹 تم مسح القالب المحفوظ للاختبار', 'info');
                
                // اختبار تحميل القالب
                await bugBountyCore.preloadTemplate();
                
                if (bugBountyCore.cachedTemplate) {
                    log(`✅ تم تحميل القالب بنجاح (${bugBountyCore.cachedTemplate.length} حرف)`, 'success');
                    log('💾 القالب محفوظ في الذاكرة', 'success');
                } else {
                    log('❌ فشل في تحميل القالب', 'error');
                }
                
                updateSystemStatus();
            } catch (error) {
                log(`❌ خطأ في اختبار تحميل القالب: ${error.message}`, 'error');
            }
        }

        async function testCachedTemplate() {
            if (!bugBountyCore) {
                log('❌ النظام غير مهيأ', 'error');
                return;
            }

            try {
                log('🔄 اختبار استخدام القالب المحفوظ...', 'info');
                
                if (bugBountyCore.cachedTemplate) {
                    log(`✅ القالب متاح في الذاكرة (${bugBountyCore.cachedTemplate.length} حرف)`, 'success');
                    
                    // اختبار سرعة الوصول
                    const startTime = performance.now();
                    const template = bugBountyCore.cachedTemplate;
                    const endTime = performance.now();
                    
                    log(`⚡ سرعة الوصول للقالب: ${(endTime - startTime).toFixed(2)} مللي ثانية`, 'success');
                    log('💨 الوصول فوري من الذاكرة', 'success');
                } else {
                    log('⚠️ لا يوجد قالب محفوظ في الذاكرة', 'warning');
                    log('🔄 محاولة تحميل القالب أولاً...', 'info');
                    await testPreloadTemplate();
                }
                
                updateSystemStatus();
            } catch (error) {
                log(`❌ خطأ في اختبار القالب المحفوظ: ${error.message}`, 'error');
            }
        }

        async function testExportWithCache() {
            if (!bugBountyCore) {
                log('❌ النظام غير مهيأ', 'error');
                return;
            }

            try {
                log('🔄 اختبار التصدير مع التخزين المؤقت...', 'info');
                
                // التأكد من وجود القالب
                if (!bugBountyCore.cachedTemplate) {
                    log('🔄 تحميل القالب أولاً...', 'info');
                    await bugBountyCore.preloadTemplate();
                }
                
                if (bugBountyCore.cachedTemplate) {
                    log('✅ القالب جاهز للتصدير', 'success');
                    
                    // محاكاة عملية التصدير
                    const mockData = {
                        vulnerabilities: [],
                        total_vulnerabilities: 0,
                        pages_processed: 1,
                        scan_timestamp: new Date().toISOString()
                    };
                    
                    log('🔄 محاكاة عملية التصدير...', 'info');
                    
                    // اختبار استبدال المتغيرات
                    let testTemplate = bugBountyCore.cachedTemplate;
                    testTemplate = testTemplate.replace(/{{TARGET_URL}}/g, 'https://example.com');
                    testTemplate = testTemplate.replace(/{{TOTAL_VULNERABILITIES}}/g, '0');
                    
                    log('✅ تم اختبار استبدال المتغيرات بنجاح', 'success');
                    log('🎯 النظام جاهز للتصدير الفعلي', 'success');
                } else {
                    log('❌ فشل في تحميل القالب للتصدير', 'error');
                }
                
                updateSystemStatus();
            } catch (error) {
                log(`❌ خطأ في اختبار التصدير: ${error.message}`, 'error');
            }
        }

        function updateSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            let status = '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">';
            
            status += '<div>';
            status += `<strong>🛡️ BugBountyCore:</strong> ${bugBountyCore ? '✅ متاح' : '❌ غير متاح'}<br>`;
            status += `<strong>💾 القالب المحفوظ:</strong> ${bugBountyCore?.cachedTemplate ? '✅ محفوظ' : '❌ غير محفوظ'}<br>`;
            status += `<strong>📏 حجم القالب:</strong> ${bugBountyCore?.cachedTemplate ? bugBountyCore.cachedTemplate.length + ' حرف' : 'غير متاح'}<br>`;
            status += '</div>';
            
            status += '<div>';
            status += `<strong>🔧 دالة preloadTemplate:</strong> ${bugBountyCore && typeof bugBountyCore.preloadTemplate === 'function' ? '✅ متاحة' : '❌ غير متاحة'}<br>`;
            status += `<strong>🌐 الخادم:</strong> ${window.location.origin}<br>`;
            status += `<strong>⏰ آخر تحديث:</strong> ${new Date().toLocaleTimeString('ar-SA')}<br>`;
            status += '</div>';
            
            status += '</div>';
            statusDiv.innerHTML = status;
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(initializeSystem, 1000);
        });
    </script>
</body>
</html>
