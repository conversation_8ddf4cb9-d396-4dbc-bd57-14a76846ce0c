<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البيانات الحقيقية للثغرات المكتشفة والمختبرة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.2);
            border-left-color: #f44336;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border-left-color: #4CAF50;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left-color: #ff9800;
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .vulnerability-data {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 3px solid #4CAF50;
            font-family: 'Courier New', monospace;
        }
        .real-data {
            color: #4CAF50;
            font-weight: bold;
        }
        .fake-data {
            color: #f44336;
            font-weight: bold;
        }
        h1, h2 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار البيانات الحقيقية للثغرات المكتشفة والمختبرة</h1>
        
        <div class="test-section">
            <h2>📊 معلومات النظام</h2>
            <div id="systemInfo"></div>
        </div>

        <div class="test-section">
            <h2>🧪 اختبار استخراج البيانات الحقيقية</h2>
            <button onclick="testRealVulnerabilityData()">اختبار البيانات الحقيقية</button>
            <button onclick="testWithMockData()">اختبار مع بيانات وهمية</button>
            <button onclick="testContextExtraction()">اختبار استخراج من السياق</button>
            <button onclick="runAllRealDataTests()">تشغيل جميع الاختبارات</button>
            
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>📋 نتائج الاختبار</h2>
            <div id="finalResults"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;

        // تهيئة النظام
        async function initializeSystem() {
            try {
                updateSystemInfo('🔄 تهيئة BugBountyCore...');
                bugBountyCore = new BugBountyCore();
                updateSystemInfo('✅ تم تهيئة BugBountyCore بنجاح');
                return true;
            } catch (error) {
                updateSystemInfo('❌ فشل في تهيئة BugBountyCore: ' + error.message);
                return false;
            }
        }

        // تحديث معلومات النظام
        function updateSystemInfo(message) {
            document.getElementById('systemInfo').innerHTML = `
                <div class="success">${message}</div>
                <p>الوقت: ${new Date().toLocaleString('ar')}</p>
                <p>نسخة النظام: v4.0 - البيانات الحقيقية للثغرات المكتشفة والمختبرة</p>
            `;
        }

        // اختبار البيانات الحقيقية للثغرات
        async function testRealVulnerabilityData() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.innerHTML += '<div class="test-section warning">🔄 اختبار البيانات الحقيقية للثغرات...</div>';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                // إنشاء ثغرة مع بيانات حقيقية مكتشفة ومختبرة
                const realVulnerability = {
                    name: 'SQL Injection',
                    type: 'SQL Injection',
                    target_url: 'http://testphp.vulnweb.com/artists.php',
                    payload: "' UNION SELECT 1,username,password FROM users--",
                    response: "HTTP/1.1 200 OK - تم استخراج 25 مستخدم من قاعدة البيانات",
                    evidence: "تم تأكيد SQL Injection من خلال استخراج بيانات المستخدمين الحقيقية",
                    response_code: 200,
                    testing_results: {
                        payload: "' OR 1=1--",
                        response: "تم تنفيذ الاستعلام بنجاح",
                        evidence: "تم الوصول لقاعدة البيانات"
                    }
                };
                
                // اختبار استخراج البيانات الحقيقية
                const extractedPayload = bugBountyCore.generateRealPayloadForVulnerability(realVulnerability);
                const extractedResponse = bugBountyCore.generateRealResponseForVulnerability(realVulnerability);
                const extractedEvidence = bugBountyCore.generateRealEvidenceForVulnerability(realVulnerability);
                
                // التحقق من أن البيانات المستخرجة هي البيانات الحقيقية
                const isRealPayload = extractedPayload === realVulnerability.payload;
                const isRealResponse = extractedResponse === realVulnerability.response;
                const isRealEvidence = extractedEvidence === realVulnerability.evidence;
                
                resultDiv.innerHTML += `
                    <div class="test-section ${isRealPayload && isRealResponse && isRealEvidence ? 'success' : 'error'}">
                        <h3>نتائج استخراج البيانات الحقيقية:</h3>
                        <div class="vulnerability-data">
                            <strong>Payload المستخرج:</strong><br>
                            <span class="${isRealPayload ? 'real-data' : 'fake-data'}">${extractedPayload}</span><br><br>
                            
                            <strong>Response المستخرج:</strong><br>
                            <span class="${isRealResponse ? 'real-data' : 'fake-data'}">${extractedResponse}</span><br><br>
                            
                            <strong>Evidence المستخرج:</strong><br>
                            <span class="${isRealEvidence ? 'real-data' : 'fake-data'}">${extractedEvidence}</span><br><br>
                            
                            <strong>النتيجة:</strong> ${isRealPayload && isRealResponse && isRealEvidence ? '✅ تم استخراج البيانات الحقيقية بنجاح' : '❌ تم توليد بيانات افتراضية بدلاً من الحقيقية'}
                        </div>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-section error">❌ فشل اختبار البيانات الحقيقية: ' + error.message + '</div>';
            }
        }

        // اختبار مع بيانات وهمية (بدون بيانات حقيقية)
        async function testWithMockData() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.innerHTML += '<div class="test-section warning">🔄 اختبار مع بيانات وهمية...</div>';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                // إنشاء ثغرة بدون بيانات حقيقية
                const mockVulnerability = {
                    name: 'XSS',
                    type: 'XSS',
                    target_url: 'http://testphp.vulnweb.com/search.php'
                    // لا توجد بيانات حقيقية مكتشفة
                };
                
                // اختبار استخراج البيانات
                const extractedPayload = bugBountyCore.generateRealPayloadForVulnerability(mockVulnerability);
                const extractedResponse = bugBountyCore.generateRealResponseForVulnerability(mockVulnerability);
                const extractedEvidence = bugBountyCore.generateRealEvidenceForVulnerability(mockVulnerability);
                
                // التحقق من أن النظام يستخرج من السياق بدلاً من توليد بيانات افتراضية
                const hasContextData = extractedPayload.includes('testphp.vulnweb.com') || 
                                     extractedResponse.includes('testphp.vulnweb.com') || 
                                     extractedEvidence.includes('testphp.vulnweb.com');
                
                resultDiv.innerHTML += `
                    <div class="test-section ${hasContextData ? 'success' : 'warning'}">
                        <h3>نتائج الاستخراج من السياق:</h3>
                        <div class="vulnerability-data">
                            <strong>Payload المستخرج:</strong><br>
                            ${extractedPayload}<br><br>
                            
                            <strong>Response المستخرج:</strong><br>
                            ${extractedResponse}<br><br>
                            
                            <strong>Evidence المستخرج:</strong><br>
                            ${extractedEvidence}<br><br>
                            
                            <strong>النتيجة:</strong> ${hasContextData ? '✅ تم الاستخراج من السياق بنجاح' : '⚠️ تم توليد بيانات عامة'}
                        </div>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-section error">❌ فشل اختبار البيانات الوهمية: ' + error.message + '</div>';
            }
        }

        // اختبار استخراج من السياق
        async function testContextExtraction() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.innerHTML += '<div class="test-section warning">🔄 اختبار استخراج من السياق...</div>';
            
            try {
                if (!bugBountyCore) await initializeSystem();
                
                // إنشاء ثغرة مع وصف يحتوي على معلومات
                const contextVulnerability = {
                    name: 'Command Injection',
                    type: 'Command Injection',
                    target_url: 'http://testphp.vulnweb.com/exec.php',
                    description: 'تم اكتشاف Command Injection باستخدام payload: ; cat /etc/passwd والحصول على status: 200 وتم confirmed استخراج ملف النظام'
                };
                
                // اختبار استخراج البيانات من السياق
                const extractedPayload = bugBountyCore.extractPayloadFromContext(contextVulnerability);
                const extractedResponse = bugBountyCore.extractResponseFromContext(contextVulnerability);
                const extractedEvidence = bugBountyCore.extractEvidenceFromContext(contextVulnerability);
                
                // التحقق من جودة الاستخراج
                const goodPayload = extractedPayload.includes('cat /etc/passwd');
                const goodResponse = extractedResponse.includes('200');
                const goodEvidence = extractedEvidence.includes('confirmed');
                
                resultDiv.innerHTML += `
                    <div class="test-section ${goodPayload && goodResponse && goodEvidence ? 'success' : 'warning'}">
                        <h3>نتائج الاستخراج من السياق:</h3>
                        <div class="vulnerability-data">
                            <strong>الوصف الأصلي:</strong><br>
                            ${contextVulnerability.description}<br><br>
                            
                            <strong>Payload المستخرج:</strong><br>
                            <span class="${goodPayload ? 'real-data' : 'fake-data'}">${extractedPayload}</span><br><br>
                            
                            <strong>Response المستخرج:</strong><br>
                            <span class="${goodResponse ? 'real-data' : 'fake-data'}">${extractedResponse}</span><br><br>
                            
                            <strong>Evidence المستخرج:</strong><br>
                            <span class="${goodEvidence ? 'real-data' : 'fake-data'}">${extractedEvidence}</span><br><br>
                            
                            <strong>النتيجة:</strong> ${goodPayload && goodResponse && goodEvidence ? '✅ تم الاستخراج من السياق بدقة' : '⚠️ يحتاج تحسين الاستخراج'}
                        </div>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-section error">❌ فشل اختبار استخراج السياق: ' + error.message + '</div>';
            }
        }

        // تشغيل جميع الاختبارات
        async function runAllRealDataTests() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('finalResults').innerHTML = '';
            
            const initialized = await initializeSystem();
            if (!initialized) return;
            
            await testRealVulnerabilityData();
            await testWithMockData();
            await testContextExtraction();
            
            // عرض النتائج النهائية
            document.getElementById('finalResults').innerHTML = `
                <div class="test-section success">
                    <h3>📊 ملخص النتائج</h3>
                    <p>✅ تم تطبيق النظام v4 لاستخراج البيانات الحقيقية من الثغرات المكتشفة والمختبرة</p>
                    <p>🎯 النظام يعطي الأولوية للبيانات الحقيقية المكتشفة قبل التوليد الافتراضي</p>
                    <p>🔍 في حالة عدم وجود بيانات حقيقية، يستخرج النظام من السياق المتاح</p>
                    <p>🚫 تم إلغاء توليد البيانات الافتراضية/المتخصصة</p>
                </div>
            `;
        }

        // تهيئة الصفحة
        window.onload = function() {
            updateSystemInfo('🔄 جاري تحميل نظام البيانات الحقيقية...');
            setTimeout(() => {
                updateSystemInfo('✅ تم تحميل النظام بنجاح - جاهز لاختبار البيانات الحقيقية');
            }, 1000);
        };
    </script>
</body>
</html>
