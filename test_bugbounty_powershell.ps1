# اختبار إصلاح Bug Bounty v4.0 باستخدام PowerShell
Write-Host "🔧 اختبار إصلاح Bug Bounty v4.0" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# 1. التحقق من وجود الملف الأساسي
$coreFile = "assets\modules\bugbounty\BugBountyCore.js"
if (Test-Path $coreFile) {
    Write-Host "✅ ملف BugBountyCore.js موجود" -ForegroundColor Green
    
    # قراءة محتوى الملف
    $content = Get-Content $coreFile -Raw
    
    # 2. التحقق من وجود الدوال المطلوبة
    Write-Host "`n🔍 التحقق من الدوال المطلوبة:" -ForegroundColor Yellow
    
    $requiredFunctions = @(
        "async performLocalComprehensiveAnalysis\(",
        "async attemptWAFBypass\(",
        "async attemptInputValidationBypass\(",
        "async attemptAuthenticationBypass\(",
        "async attemptSessionBypass\(",
        "async attemptCSRFBypass\(",
        "async attemptCSPBypass\(",
        "async attemptEncodingBypass\(",
        "async attemptAdvancedBypass\(",
        "async performPersistentVulnerabilityScanning\("
    )
    
    foreach ($func in $requiredFunctions) {
        $matches = [regex]::Matches($content, $func)
        if ($matches.Count -gt 0) {
            Write-Host "✅ $func - موجودة ($($matches.Count) مرة)" -ForegroundColor Green
        } else {
            Write-Host "❌ $func - مفقودة" -ForegroundColor Red
        }
    }
    
    # 3. التحقق من عدم وجود أخطاء "vuln is not defined"
    Write-Host "`n🔍 التحقق من أخطاء 'vuln is not defined':" -ForegroundColor Yellow
    $vulnErrors = [regex]::Matches($content, "vuln is not defined")
    if ($vulnErrors.Count -eq 0) {
        Write-Host "✅ لا توجد أخطاء 'vuln is not defined'" -ForegroundColor Green
    } else {
        Write-Host "❌ وجدت $($vulnErrors.Count) أخطاء 'vuln is not defined'" -ForegroundColor Red
    }
    
    # 4. التحقق من عدد الدوال المكررة
    Write-Host "`n🔍 التحقق من الدوال المكررة:" -ForegroundColor Yellow
    $duplicateCheck = [regex]::Matches($content, "async performLocalComprehensiveAnalysis\(")
    if ($duplicateCheck.Count -eq 1) {
        Write-Host "✅ دالة performLocalComprehensiveAnalysis موجودة مرة واحدة فقط" -ForegroundColor Green
    } else {
        Write-Host "❌ دالة performLocalComprehensiveAnalysis موجودة $($duplicateCheck.Count) مرة (يجب أن تكون مرة واحدة)" -ForegroundColor Red
    }
    
    # 5. إحصائيات الملف
    Write-Host "`n📊 إحصائيات الملف:" -ForegroundColor Yellow
    $lines = ($content -split "`n").Count
    $size = (Get-Item $coreFile).Length / 1KB
    Write-Host "📄 عدد الأسطر: $lines" -ForegroundColor Cyan
    Write-Host "📏 حجم الملف: $([math]::Round($size, 2)) KB" -ForegroundColor Cyan
    
    # 6. اختبار تحميل الملف في Node.js
    Write-Host "`n🧪 اختبار تحميل الملف:" -ForegroundColor Yellow
    try {
        $testScript = @"
const fs = require('fs');
try {
    const content = fs.readFileSync('$coreFile', 'utf8');
    console.log('✅ تم تحميل الملف بنجاح');
    console.log('📊 عدد الأسطر:', content.split('\n').length);
    
    // فحص syntax أساسي
    const syntaxErrors = [];
    if (content.includes('async performLocalComprehensiveAnalysis(')) {
        console.log('✅ دالة performLocalComprehensiveAnalysis موجودة');
    } else {
        console.log('❌ دالة performLocalComprehensiveAnalysis مفقودة');
    }
    
    if (content.includes('async attemptWAFBypass(')) {
        console.log('✅ دالة attemptWAFBypass موجودة');
    } else {
        console.log('❌ دالة attemptWAFBypass مفقودة');
    }
    
} catch (error) {
    console.error('❌ خطأ في تحميل الملف:', error.message);
}
"@
        
        $testScript | Out-File -FilePath "temp_test.js" -Encoding UTF8
        $result = node "temp_test.js" 2>&1
        Write-Host $result -ForegroundColor White
        Remove-Item "temp_test.js" -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "❌ فشل في اختبار Node.js: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 7. النتيجة النهائية
    Write-Host "`n🎯 النتيجة النهائية:" -ForegroundColor Yellow
    $allFunctionsExist = $true
    foreach ($func in $requiredFunctions) {
        if ([regex]::Matches($content, $func).Count -eq 0) {
            $allFunctionsExist = $false
            break
        }
    }
    
    if ($allFunctionsExist -and $vulnErrors.Count -eq 0 -and $duplicateCheck.Count -eq 1) {
        Write-Host "🎉 جميع الإصلاحات مطبقة بنجاح!" -ForegroundColor Green
        Write-Host "✅ يجب أن يعمل النظام الآن بدون أخطاء 'vuln is not defined'" -ForegroundColor Green
    } else {
        Write-Host "⚠️ لا تزال هناك مشاكل تحتاج إصلاح" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ ملف BugBountyCore.js غير موجود" -ForegroundColor Red
}

Write-Host "`n🔚 انتهى الاختبار" -ForegroundColor Cyan
