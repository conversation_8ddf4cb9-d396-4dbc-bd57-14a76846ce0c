# Bug Bounty v4.0 Fixes Verification - Basic Version

Write-Host "Starting Bug Bounty v4.0 fixes verification..." -ForegroundColor Cyan

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"

if (-not (Test-Path $bugBountyFile)) {
    Write-Host "ERROR: BugBountyCore.js file not found" -ForegroundColor Red
    exit 1
}

Write-Host "Found BugBountyCore.js file" -ForegroundColor Green

$fileContent = Get-Content $bugBountyFile -Raw -Encoding UTF8

Write-Host "Checking for new real data functions..." -ForegroundColor Yellow

$newFunctions = @(
    "extractParameterFromContext",
    "extractErrorMessagesFromVulnerability", 
    "generateUserPrivacyImpact",
    "generateSystemStabilityImpact",
    "calculateResponseTimeFromVulnerability",
    "calculateRequestSizeFromVulnerability",
    "calculateResponseSizeFromVulnerability", 
    "calculateConfidenceLevelFromVulnerability",
    "estimateAffectedTables"
)

$foundFunctions = 0
foreach ($func in $newFunctions) {
    if ($fileContent -match "$func\s*\(") {
        $foundFunctions++
        Write-Host "FOUND: $func" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $func" -ForegroundColor Red
    }
}

Write-Host "Checking Math.random usage..." -ForegroundColor Yellow

$mathRandomMatches = [regex]::Matches($fileContent, "Math\.random\(\)")
Write-Host "Total Math.random usage: $($mathRandomMatches.Count)" -ForegroundColor Cyan

Write-Host "Checking real data function calls..." -ForegroundColor Yellow

$realDataCallPatterns = @(
    "extractParameterFromContext\(",
    "generateEvidenceFromVulnerabilityData\(",
    "generateRealImpactFromVulnerability\(",
    "extractErrorMessagesFromVulnerability\(",
    "calculateResponseTimeFromVulnerability\(",
    "calculateConfidenceLevelFromVulnerability\("
)

$foundRealDataCalls = 0
foreach ($pattern in $realDataCallPatterns) {
    $matches = [regex]::Matches($fileContent, $pattern)
    $foundRealDataCalls += $matches.Count
    Write-Host "Calls to $pattern : $($matches.Count)" -ForegroundColor Green
}

Write-Host "SUMMARY RESULTS:" -ForegroundColor Cyan

$tests = @{
    "New Functions Added" = ($foundFunctions -eq $newFunctions.Count)
    "Real Data Calls Found" = ($foundRealDataCalls -gt 0)
}

$passedTests = 0
foreach ($test in $tests.GetEnumerator()) {
    $status = if ($test.Value) { "PASS" } else { "FAIL" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "$status - $($test.Key)" -ForegroundColor $color
    if ($test.Value) { $passedTests++ }
}

Write-Host "Final Result: $passedTests/$($tests.Count) tests passed" -ForegroundColor Cyan

if ($passedTests -eq $tests.Count) {
    Write-Host "SUCCESS! All fixes are working correctly." -ForegroundColor Green
} else {
    Write-Host "Some fixes need additional review." -ForegroundColor Yellow
}

Write-Host "Additional Details:" -ForegroundColor Cyan
Write-Host "- New functions added: $foundFunctions/$($newFunctions.Count)" -ForegroundColor White
Write-Host "- Real data function calls found: $foundRealDataCalls" -ForegroundColor White
Write-Host "- Total Math.random usage: $($mathRandomMatches.Count)" -ForegroundColor White

Write-Host "Verification completed." -ForegroundColor Green
